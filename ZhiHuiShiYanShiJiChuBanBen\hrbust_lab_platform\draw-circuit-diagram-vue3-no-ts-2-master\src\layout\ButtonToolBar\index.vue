<template>
  <!-- 控制按钮 -->
  <div class="tool-bar">
    <div class="toolbar-flow">
      <!-- 连线模式开关 -->
      <div class="mode-wrapper">
        <el-icon class="mode-icon" size="16px"><Connection /></el-icon>
        <span class="mode-label">连线模式</span>
        <el-switch
          v-model="isLineModeEnabled"
          size="default"
          @change="toggleLineMode"
          active-color="#10b981"
          inactive-color="#e2e8f0"
        />
      </div>

      <!-- 隐藏旋转按钮组 - 如需重新启用，请取消注释
      <div class="rotate-group">
        <el-button :disabled="!editComponentEnabled" @click="rotateComponent(-90)" class="action-btn rotate-btn rotate-btn-small" size="small">
          <svg-icon name="rotate-left" width="12px" height="12px" color="white"></svg-icon>
          <span>左旋</span>
        </el-button>

        <el-button :disabled="!editComponentEnabled" @click="rotateComponent(90)" class="action-btn rotate-btn rotate-btn-small" size="small">
          <svg-icon name="rotate-right" width="12px" height="12px" color="white"></svg-icon>
          <span>右旋</span>
        </el-button>
      </div>
      -->

      <!-- 编辑操作按钮 -->
      <el-button :disabled="!editComponentEnabled" @click="openDrawer" class="action-btn edit-btn" size="small">
        <el-icon size="14px"><Edit /></el-icon>
        <span>编辑组件</span>
      </el-button>

      <el-tooltip content="选中组件后点击删除，或按Delete键快速删除" placement="bottom">
        <el-button :disabled="!deleteComponentEnabled" @click="confirmDeleteComponent" class="action-btn delete-btn" size="small">
          <el-icon size="14px"><CircleClose /></el-icon>
          <span>删除组件</span>
        </el-button>
      </el-tooltip>

      <el-tooltip content="选中连线后点击删除，或按Delete键快速删除" placement="bottom">
        <el-button @click="deleteSelectedLine" :disabled="selectedLine === null" class="action-btn delete-btn" size="small">
          <el-icon size="14px"><CircleClose /></el-icon>
          <span>删除连线</span>
        </el-button>
      </el-tooltip>

      <!-- 隐藏文本框相关按钮 - 如需重新启用，请取消注释
      <el-button @click="addTextBox" class="action-btn add-btn" size="small">
        <el-icon size="14px"><Plus /></el-icon>
        <span>添加文本框</span>
      </el-button>

      <el-button :disabled="!editTextBoxEnabled" @click="openTextBoxDrawer" class="action-btn edit-btn" size="small">
        <el-icon size="14px"><Edit /></el-icon>
        <span>编辑文本框</span>
      </el-button>
      -->

      <!-- 导出按钮 -->
      <export-png-svg targetId="canvas-area" svgSelector=".grid" />

      <!-- 校验按钮 -->
      <el-button @click="verify" class="action-btn verify-btn" size="small">
        <svg-icon name="verify" color="white" width="14px"></svg-icon>
        <span>校验</span>
      </el-button>

      <!-- 保存模板按钮 -->
      <el-button v-show="canShowButton" @click="saveConnectionToTemplate(courseName, experimentName)" class="action-btn save-btn" size="small">
        <svg-icon name="save" color="white" width="14px"></svg-icon>
        <span>保存模板</span>
      </el-button>

      <el-button @click="resetData" class="action-btn reset-btn" size="small">
        <el-icon size="14px"><RefreshLeft /></el-icon>
        <span>重置</span>
      </el-button>

      <el-button @click="finished" class="action-btn finish-btn" size="small">
        <el-icon size="14px"><Check /></el-icon>
        <span>完成</span>
      </el-button>

      <!-- 调试按钮 -->
      <div class="debug-separator"></div>

      <!-- 隐藏调试按钮 - 如需重新启用，请取消注释
      <el-button @click="debugTestPointRelations" class="debug-btn info-debug" size="small">
        <el-icon size="14px"><InfoFilled /></el-icon>
        <span>测点关系</span>
      </el-button>

      <el-button @click="debugUserConnections" class="debug-btn warning-debug" size="small">
        <el-icon size="14px"><InfoFilled /></el-icon>
        <span>用户连接</span>
      </el-button>

      <el-button @click="debugPowerComponent" class="debug-btn danger-debug" size="small">
        <el-icon size="14px"><InfoFilled /></el-icon>
        <span>电源组件</span>
      </el-button>
      -->

      <!-- 隐藏连接点调试按钮 - 如需重新启用，请取消注释
      <el-button @click="toggleConnectionPoints" class="debug-btn success-debug" size="small">
        <el-icon size="14px"><View /></el-icon>
        <span>连接点</span>
      </el-button>
      -->

      <!-- 跳转实验环境按钮 -->
      <el-button @click="redirectToExperiment" class="action-btn experiment-btn" size="small">
        <el-icon size="14px"><View /></el-icon>
        <span>跳转到实验环境</span>
      </el-button>
    </div>
  </div>
</template>

<script setup name="ButtonToolBar">
  import { ref } from 'vue'
  import ExportPngSvg from '@/components/ExportPngSvg/index.vue'
  import { storeToRefs } from 'pinia'
  import { ElNotification, ElMessage, ElMessageBox } from 'element-plus'
  import { saveData, loadData, resetData, findSurroundingComponents } from '@/utils/canvasDataManager.js'
  import { saveAsTemplate, matchWithTemplate, getStoredTemplates, displayMatchResult } from '@/utils/templateMatcher.js'
  import { validateURLParam } from '@/utils/before/validateURLParam.js'
  import { View, InfoFilled, Edit, CircleClose, Plus, RefreshLeft, Check, Connection } from '@element-plus/icons-vue'
  import { componentsData } from '@/layout/ComponentsPanel/data/componentsData.js'

  /**
   * store: 组件信息
   */
  import { useComponentsInfoStore } from '@/store/componentsInfo.js'
  const componentsInfoStore = useComponentsInfoStore()
  const { components } = storeToRefs(componentsInfoStore)

  /**
   * store: 按钮工具栏状态
   */
  import { useButtonBarInfoStore } from '@/store/buttonBarInfo.js'
  const buttonBarInfoStore = useButtonBarInfoStore()
  const { deleteComponentEnabled, editComponentEnabled, editTextBoxEnabled } = storeToRefs(buttonBarInfoStore)
  const { openDrawer, openTextBoxDrawer } = buttonBarInfoStore

  /**
   * store: 连线模式
   */
  import { useLineModeStore } from '@/store/lineMode.js'
  const lineModeStore = useLineModeStore()
  const { lineMode, lineModeText } = storeToRefs(lineModeStore)

  /**
   * store: 连线状态
   */
  import { useLineStateStore } from '@/store/lineState.js'
  const lineStateStore = useLineStateStore()
  const { selectedLine } = storeToRefs(lineStateStore)

  /**
   * store: 连线状态
   */
  import { useVerifyStore } from '@/store/verify'
  const verifyStore = useVerifyStore()
  const {} = storeToRefs(verifyStore)
  const { validateConnections, generateConnectionRelationships, extractComponentType, buildTopology } = verifyStore

  /**
   * 当前电路图数据
   */
  import { useCurrentUserCircuitDataStore } from '@/store/currentUserCircuitData.js'
  const { currentUserCircuitData } = storeToRefs(useCurrentUserCircuitDataStore())

  /**
   *删除连线 hooks
   */
  import useDeleteLine from '@/hooks/useDeleteLine.js'
  const { deleteSelectedLine } = useDeleteLine()

  /**
   * 删除组件 hooks
   */

  import usedeleteComponent from '@/hooks/usedeleteComponent.js'
  const { deleteComponent } = usedeleteComponent()

  /**
   * 确认删除组件
   */
  const confirmDeleteComponent = () => {
    ElMessageBox.confirm(
      '确定要删除选中的组件吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      deleteComponent()
      ElMessage({
        type: 'success',
        message: '组件删除成功',
      })
    }).catch(() => {
      // 用户取消删除
    })
  }

  /**
   * 编辑组件 hooks
   */

  import useEditComponent from '@/hooks/useEditComponent.js'
  const { rotateComponent } = useEditComponent()

  /**
   * hooks: 文本框
   */
  import useCRUDCustomTextBox from '@/hooks/useCRUDCustomTextBox.js'
  const { addTextBox, editTextBox, deleteTextBox } = useCRUDCustomTextBox()

  /**
   * 校验逻辑
   */
  import { get } from '@/api/circuitDataTemplate.js'
  // 处理【解构】电路图的函数
  const verify = async () => {
    try {
      // 获取【指定课程-项目】的模板数据
      const courseName = localStorage.getItem('courseName') // 从本地存储获取[课程名]
      const experimentName = localStorage.getItem('experimentName') // 从本地存储获取[实验名]
      let templateData = null

      await get({ courseName, experimentName })
        .then((res) => {
          const response = res.data
          if (response.code === 200) {
            // 🔧 处理模板数据和测点关系
            console.log('🔍 接口返回的完整数据:', response.data)

            // 尝试获取完整的模板数据
            if (response.data.templateData) {
              templateData = JSON.parse(response.data.templateData)
              ElNotification({ title: '获取模板数据成功！', type: 'success' })
            } else {
              // 兼容旧格式：只有连接关系，需要构建完整的模板数据
              const connections = JSON.parse(response.data.connections)

              // 🔧 检查是否有测点关系数据
              let testPointRequirements = null
              if (response.data.testPointRelations) {
                console.log('🔍 发现测点关系数据:', response.data.testPointRelations)
                testPointRequirements = JSON.parse(response.data.testPointRelations)
                console.log('🔍 解析后的测点关系:', testPointRequirements)

                // 🔧 将测点关系数据存储到localStorage，供实验环境使用
                try {
                  localStorage.setItem('testPointRelations', JSON.stringify(testPointRequirements))
                  sessionStorage.setItem('testPointRelations', JSON.stringify(testPointRequirements))
                  console.log('✅ 测点关系数据已存储到本地存储')

                  // 🔧 构建commObj格式的数据，供实验环境直接使用
                  const commObjData = {}
                  Object.keys(testPointRequirements).forEach(groupKey => {
                    const group = testPointRequirements[groupKey]
                    if (group.testPoint) {
                      const testPoint = group.testPoint
                      const identifier = testPoint.identifier
                      commObjData[identifier] = {
                        bval: testPoint.bval || testPoint.identifier,
                        channel: testPoint.channel || "1"
                      }
                    }
                  })

                  localStorage.setItem('commObjData', JSON.stringify(commObjData))
                  sessionStorage.setItem('commObjData', JSON.stringify(commObjData))
                  console.log('✅ commObj格式数据已构建并存储:', commObjData)
                } catch (storageError) {
                  console.error('❌ 存储测点关系数据时出错:', storageError)
                }
              }

              // 构建完整的模板数据结构
              templateData = {
                connections: connections,
                testPointRequirements: testPointRequirements
              }

              console.log('🔍 构建的完整模板数据:', templateData)
              ElNotification({ title: '获取模板数据和测点关系成功！', type: 'success' })
            }
          } else {
            ElNotification({ title: '获取模板数据失败！', type: 'error' })
          }
        })
        .catch((err) => {
          ElNotification({ title: '获取模板数据失败！', message: err, type: 'error' })
          return
        })

      if (!templateData) {
        ElNotification.error({ title: '模板数据为空', duration: 1000 })
        return
      }

      // 🔧 调试：检查模板数据结构
      console.log('🔍 模板数据结构:', templateData)
      console.log('🔍 是否包含测点要求:', !!templateData.testPointRequirements)
      if (templateData.testPointRequirements) {
        console.log('🔍 测点要求内容:', templateData.testPointRequirements)
      }

      // 校验连接关系和测点关系
      const validateResult = validateConnections(templateData)
      // console.log('@@@ 校验结果 validateResult ', validateResult)

      if (validateResult === true) {
        // 全部校验成功
        ElNotification.success({
          title: '校验通过',
          duration: 2000
        })
        return true // 返回校验成功
      } else {
        // 校验失败
        if (validateResult.overall === false) {
          // 新格式：包含连接关系和测点关系的校验结果
          const connectionResult = validateResult.connectionValidation
          const testPointResult = validateResult.testPointValidation

          const connectionPassed = connectionResult === true
          const testPointPassed = testPointResult === true

          // 根据结果显示简单的通过/不通过状态
          if (connectionPassed && testPointPassed) {
            ElNotification.success({ title: '校验通过', duration: 2000 })
            return true // 校验通过
          } else if (connectionPassed && !testPointPassed) {
            // 🔧 显示详细的测点错误信息
            showDetailedTestPointErrors(testPointResult)
            return false // 校验失败
          } else if (!connectionPassed && testPointPassed) {
            // 🔧 显示详细的连接关系错误信息
            showDetailedConnectionErrors(connectionResult)
            return false // 校验失败
          } else {
            // 🔧 显示详细的综合错误信息
            showDetailedCombinedErrors(connectionResult, testPointResult)
            return false // 校验失败
          }

        } else if (validateResult.topologyMatch === false) {
          // 处理单独的拓扑比较结果
          ElNotification.error({
            title: '电路拓扑结构不匹配',
            message: '请检查组件之间的连接关系',
            duration: 3000
          })
          return false // 校验失败
        } else {
          // 旧格式：只有连接关系的校验结果
          const result = validateResult.missingConnections?.length === 0 && validateResult.extraConnections?.length === 0
          if (result) {
            ElNotification.success({ title: '校验通过', duration: 2000 })
            return true // 校验通过
          } else {
            ElNotification.error({ title: '校验不通过', duration: 2000 })
            return false // 校验失败
          }
        }
      }
    } catch (error) {
      console.error('校验过程中出错:', error)
      ElNotification.error({ title: '校验过程中出错', message: error.message, duration: 2000 })
    }
  }

  /**
   * 🔧 显示详细的测点错误信息
   */
  const showDetailedTestPointErrors = (testPointResult) => {
    if (!testPointResult || testPointResult.valid !== false) return

    const { missingGroups, extraGroups, summary } = testPointResult

    let errorMessage = `测点分组校验失败:\n`
    errorMessage += `• 要求: ${summary.required}个分组\n`
    errorMessage += `• 实际: ${summary.actual}个分组\n`

    if (missingGroups && missingGroups.length > 0) {
      errorMessage += `\n❌ 缺少的测点分组 (${missingGroups.length}个):\n`
      missingGroups.forEach(group => {
        errorMessage += `  • ${group.description}\n`
      })
    }

    if (extraGroups && extraGroups.length > 0) {
      errorMessage += `\n➕ 多余的测点分组 (${extraGroups.length}个):\n`
      extraGroups.forEach(group => {
        errorMessage += `  • ${group.description}\n`
      })
    }

    ElNotification.error({
      title: '测点分组校验失败',
      message: errorMessage,
      duration: 8000,
      dangerouslyUseHTMLString: false
    })
  }

  /**
   * 🔧 显示详细的连接关系错误信息
   */
  const showDetailedConnectionErrors = (connectionResult) => {
    if (!connectionResult || connectionResult.topologyMatch !== false) return

    const { connectionDifferences, summary, missingNodes, extraNodes } = connectionResult

    let errorMessage = `电路连接关系校验失败:\n`
    errorMessage += `• 要求: ${summary.required}个连接\n`
    errorMessage += `• 实际: ${summary.actual}个连接\n`

    if (missingNodes && missingNodes.length > 0) {
      errorMessage += `\n❌ 缺少的组件 (${missingNodes.length}个):\n`
      missingNodes.forEach(node => {
        errorMessage += `  • ${node}\n`
      })
    }

    if (extraNodes && extraNodes.length > 0) {
      errorMessage += `\n➕ 多余的组件 (${extraNodes.length}个):\n`
      extraNodes.forEach(node => {
        errorMessage += `  • ${node}\n`
      })
    }

    if (connectionDifferences && connectionDifferences.length > 0) {
      errorMessage += `\n🔗 连接差异 (${connectionDifferences.length}个组件):\n`
      connectionDifferences.forEach(diff => {
        errorMessage += `  • ${diff.node}: 要求${diff.baseCount}个连接，实际${diff.currentCount}个\n`
        if (diff.missing.length > 0) {
          errorMessage += `    缺少连接: ${diff.missing.join(', ')}\n`
        }
        if (diff.extra.length > 0) {
          errorMessage += `    多余连接: ${diff.extra.join(', ')}\n`
        }
      })
    }

    ElNotification.error({
      title: '电路连接关系校验失败',
      message: errorMessage,
      duration: 10000,
      dangerouslyUseHTMLString: false
    })
  }

  /**
   * 🔧 显示详细的综合错误信息
   */
  const showDetailedCombinedErrors = (connectionResult, testPointResult) => {
    let errorMessage = `电路校验失败:\n\n`

    // 连接关系错误
    if (connectionResult && connectionResult.topologyMatch === false) {
      const { summary } = connectionResult
      errorMessage += `🔗 连接关系问题:\n`
      errorMessage += `  • 缺少 ${summary.missing} 个连接\n`
      errorMessage += `  • 多余 ${summary.extra} 个连接\n\n`
    }

    // 测点分组错误
    if (testPointResult && testPointResult.valid === false) {
      const { summary } = testPointResult
      errorMessage += `📍 测点分组问题:\n`
      errorMessage += `  • 缺少 ${summary.missing} 个分组\n`
      errorMessage += `  • 多余 ${summary.extra} 个分组\n`
    }

    ElNotification.error({
      title: '电路校验失败',
      message: errorMessage,
      duration: 6000,
      dangerouslyUseHTMLString: false
    })
  }

  /**
   * 保存连接关系为模板 hooks
   */
  import useSaveConnectionToTemplate from '@/hooks/useSaveConnectionToTemplate.js'
  const {
    canShowButton,
    saveConnectionToTemplate
  } = useSaveConnectionToTemplate()

  /**
   * 获取到 URL 参数
   */
  const { courseName, experimentName } = validateURLParam()

  /**
   * 是否开启连线模式
   */
  const isLineModeEnabled = ref(false)

  /**
   * 切换连线模式的方法
   */
  const toggleLineMode = () => {
    if (isLineModeEnabled.value) {
      // 如果是 true，则设置为智能画线模式
      lineModeStore.setLineMode('smart')
    } else {
      // 否则关闭连线模式
      lineModeStore.setLineMode('disabled')
    }
  }

  // 样式对象
  const modeLabelStyle = {
    fontSize: '18px',
    marginRight: '5px',
    verticalAlign: 'middle',
    fontWeight: 'bold', // 加粗文字
  }

  const switchStyle = {
    verticalAlign: 'middle',
  }

  const modeWrapperStyle = {
    display: 'inline-flex',
    alignItems: 'center',
    padding: '6px 12px', // 内边距
    backgroundColor: '#f3f6f8', // 背景颜色
    borderRadius: '4px', // 圆角
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)', // 阴影效果
    position: 'relative', // 添加
    top: '-10px', // 调整位置
  }

  /**
   * hooks：点击完成按钮
   */
  import useFinished from '@/hooks/useFinished.js'
  const { finished } = useFinished()

  /**
   * 添加临时测试函数 - 跳转到实验环境（带校验）
   */
  const redirectToExperiment = async () => {
    console.log('🚀 准备跳转到实验环境，先执行校验...');

    try {
      // 先执行校验
      const verifyResult = await verify();

      // 检查校验结果
      if (!verifyResult) {
        console.log('❌ 校验未通过，禁止跳转到实验环境');
        ElMessage.warning('校验未通过，请修正电路后再试');
        return;
      }

      // 检查是否有组件
      if (!components.value || components.value.length === 0) {
        ElMessage.warning('电路图为空，无法进入实验环境');
        return;
      }

      await saveData();



      // 校验通过，继续跳转流程
      // 提前打开实验环境窗口
      const experimentWindow = window.open('/hrbust_lab_platform/draw-circuit-diagram-dist/syhj.html', '_blank');

      // 显示加载提示
      const loadingIndicator = document.createElement('div');
      loadingIndicator.innerHTML = '正在处理电路图...<br><small>这可能需要几秒钟时间</small>';
      loadingIndicator.style.position = 'fixed';
      loadingIndicator.style.top = '50%';
      loadingIndicator.style.left = '50%';
      loadingIndicator.style.transform = 'translate(-50%, -50%)';
      loadingIndicator.style.background = 'rgba(0,0,0,0.7)';
      loadingIndicator.style.color = 'white';
      loadingIndicator.style.padding = '15px 20px';
      loadingIndicator.style.borderRadius = '5px';
      loadingIndicator.style.zIndex = '9999';
      loadingIndicator.style.textAlign = 'center';
      document.body.appendChild(loadingIndicator);

      // 设置来源标志
      sessionStorage.setItem('from_circuit_editor', 'true');

      // 获取网格大小
      const gridSize = 20;
      sessionStorage.setItem('circuit_grid_size', gridSize.toString());

      localStorage.removeItem('ammeters');
      localStorage.removeItem('voltmeters');

      const cleanupDuplicateElements = (selector) => {
        const elements = document.querySelectorAll(selector);
        const seenIds = new Set();
        const elementsToRemove = [];

        elements.forEach((element) => {
          const id = element.id;
          if (seenIds.has(id)) {
            elementsToRemove.push(element);
          } else {
            seenIds.add(id);
          }
        });

        elementsToRemove.forEach(element => {
          element.remove();
        });

        return document.querySelectorAll(selector).length;
      };

      cleanupDuplicateElements('.component[data-component-type="ammeter"]');
      cleanupDuplicateElements('.component[data-component-type="voltmeter"]');

      // 获取画布元素
      const canvasElement = document.getElementById('canvas-area');
      if (!canvasElement) {
          console.error('找不到画布元素');
          return;
      }

      // 收集可变电阻组件信息
      const variableResistors = [];
      const resistorElements = document.querySelectorAll('.component[data-component-type="rheostat"]');

      resistorElements.forEach((element, index) => {
        // 获取电阻ID和标签
        const resistorId = element.id || `resistor-${index}`;
        const resistorLabel = element.dataset.label || `可变电阻${index + 1}`;

        variableResistors.push({
          id: resistorId,
          label: resistorLabel,
          // 可以添加其他需要的属性
          defaultValue: 50 // 默认电阻值百分比
        });
      });

      // 如果没有找到可变电阻，尝试其他可能的选择器
      if (variableResistors.length === 0) {
        const alternativeSelectors = [
          '.component[data-type="rheostat"]',
          '.rheostat',
          '[data-component="rheostat"]',
          '.component[data-component-type="variable-resistor"]',
          '.component[data-type="variable-resistor"]'
        ];

        for (const selector of alternativeSelectors) {
          const elements = document.querySelectorAll(selector);

          if (elements.length > 0) {
            elements.forEach((element, index) => {
              const resistorId = element.id || `resistor-${index}`;
              const resistorLabel = element.dataset.label || `可变电阻${index + 1}`;

              variableResistors.push({
                id: resistorId,
                label: resistorLabel,
                defaultValue: 50
              });
            });

            break;
          }
        }
      }

      // 如果仍然没有找到可变电阻，添加一个默认的
      if (variableResistors.length === 0) {
        variableResistors.push({
          id: 'default-resistor',
          label: '默认可变电阻',
          defaultValue: 50
        });
      }

      // 🔧 收集电流表组件信息
      const ammeters = [];
      const ammeterElements = document.querySelectorAll('.component[data-component-type="ammeter"]');

      ammeterElements.forEach((element, index) => {
        const ammeterId = element.id || `ammeter-${index}`;
        const ammeterLabel = element.dataset.label || element.textContent?.trim() || `A${index + 1}`;

        ammeters.push({
          id: ammeterId,
          identifier: ammeterLabel,
          label: ammeterLabel,
          type: 'ammeter',
          range: '0-10A',
          unit: 'A'
        });
      });

      // 如果没有找到电流表，尝试其他可能的选择器
      if (ammeters.length === 0) {
        const ammeterSelectors = [
          '.component[data-type="ammeter"]',
          '.ammeter',
          '[data-component="ammeter"]',
          '.component[data-component-type="电流表"]'
        ];

        for (const selector of ammeterSelectors) {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            elements.forEach((element, index) => {
              const ammeterId = element.id || `ammeter-${index}`;
              const ammeterLabel = element.dataset.label || element.textContent?.trim() || `A${index + 1}`;

              ammeters.push({
                id: ammeterId,
                identifier: ammeterLabel,
                label: ammeterLabel,
                type: 'ammeter',
                range: '0-10A',
                unit: 'A'
              });
            });
            break;
          }
        }
      }

      // 🔧 收集电压表组件信息
      const voltmeters = [];
      const voltmeterElements = document.querySelectorAll('.component[data-component-type="voltmeter"]');

      voltmeterElements.forEach((element, index) => {
        const voltmeterId = element.id || `voltmeter-${index}`;
        const voltmeterLabel = element.dataset.label || element.textContent?.trim() || `V${index + 1}`;

        voltmeters.push({
          id: voltmeterId,
          identifier: voltmeterLabel,
          label: voltmeterLabel,
          type: 'voltmeter',
          range: '0-30V',
          unit: 'V'
        });
      });

      // 如果没有找到电压表，尝试其他可能的选择器
      if (voltmeters.length === 0) {
        const voltmeterSelectors = [
          '.component[data-type="voltmeter"]',
          '.voltmeter',
          '[data-component="voltmeter"]',
          '.component[data-component-type="电压表"]'
        ];

        for (const selector of voltmeterSelectors) {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            elements.forEach((element, index) => {
              const voltmeterId = element.id || `voltmeter-${index}`;
              const voltmeterLabel = element.dataset.label || element.textContent?.trim() || `V${index + 1}`;

              voltmeters.push({
                id: voltmeterId,
                identifier: voltmeterLabel,
                label: voltmeterLabel,
                type: 'voltmeter',
                range: '0-30V',
                unit: 'V'
              });
            });
            break;
          }
        }
      }



      // 获取画布尺寸
      const canvasRect = canvasElement.getBoundingClientRect();
      const canvasWidth = canvasRect.width;
      const canvasHeight = canvasRect.height;

      // 收集测试点并计算百分比位置
      let measurementPoints = [];
      const testPointElements = document.querySelectorAll('.test-point');

      testPointElements.forEach((element, index) => {
          const rect = element.getBoundingClientRect();

          // 获取元素中心点相对于画布的位置
          const centerX = rect.left + rect.width/2 - canvasRect.left;
          const centerY = rect.top + rect.height/2 - canvasRect.top;

          // 计算最接近的网格交叉点
          const gridX = Math.round(centerX / gridSize);
          const gridY = Math.round(centerY / gridSize);

          // 使用网格坐标计算百分比
          const percentX = (gridX * gridSize / canvasWidth) * 100;
          const percentY = (gridY * gridSize / canvasHeight) * 100;

          // 记录原始计算值用于调试
          // console.log(`测点${index + 1}: 原始位置(${centerX},${centerY}), 网格(${gridX},${gridY}), 百分比(${percentX.toFixed(2)}%,${percentY.toFixed(2)}%)`);

          measurementPoints.push({
              id: element.dataset.id || `point-${index}`,
              label: element.dataset.label || `测点${index+1}`,
              x: percentX,
              y: percentY,
              // 保存原始计算值以便在实验环境中调试
              original: {
                  centerX, centerY,
                  gridX, gridY
              }
          });
      });

      // 保存测试点数据到sessionStorage
      const testPointsJSON = JSON.stringify(measurementPoints);
      localStorage.setItem('circuit_test_points', testPointsJSON);
      // console.log(`已保存${measurementPoints.length}个测试点，包含百分比位置信息`);

      // 🔧 保存仪表数据到localStorage
      try {
        const ammetersJSON = JSON.stringify(ammeters);
        localStorage.setItem('ammeters', ammetersJSON);

        const voltmetersJSON = JSON.stringify(voltmeters);
        localStorage.setItem('voltmeters', voltmetersJSON);

        const variableResistorsJSON = JSON.stringify(variableResistors);
        localStorage.setItem('variableResistors', variableResistorsJSON);

      } catch (storageError) {
        // 静默处理错误
      }

      // 处理电路图
      if (canvasElement) {
          try {
              const canvasClone = canvasElement.cloneNode(true);

              // 找到所有组件，确保它们可见
              const componentElements = canvasClone.querySelectorAll('.component');
              componentElements.forEach(el => {
                  el.style.visibility = 'visible';
                  el.style.display = 'block';
                  el.style.opacity = '1';
              });

              // 确保网格线可见
              const gridElements = canvasClone.querySelectorAll('svg.grid, .grid-container, .grid-line, [class*="grid"]');
              gridElements.forEach(el => {
                el.style.visibility = 'visible';
                el.style.display = 'block';
                el.style.opacity = '1';
            });
            
            // 设置背景
            canvasClone.style.background = 'white';
            canvasClone.style.backgroundColor = 'white';
            
            // 在Canvas上标记测点位置 - 使用网格坐标
            measurementPoints.forEach((point, index) => {
                // 创建测点标记元素
                const marker = document.createElement('div');
                marker.style.position = 'absolute';
                
                // 使用网格坐标计算像素位置
                const x = point.gridX * gridSize;
                const y = point.gridY * gridSize;
                
                marker.style.left = `${x}px`;
                marker.style.top = `${y}px`;
                marker.style.width = '16px';
                marker.style.height = '16px';
                marker.style.backgroundColor = '#ff5722';
                marker.style.borderRadius = '50%';
                marker.style.transform = 'translate(-50%, -50%)';
                marker.style.border = '2px solid white';
                marker.style.boxShadow = '0 0 4px rgba(0,0,0,0.5)';
                marker.style.zIndex = '1000';
                
                // 添加标签
                const label = document.createElement('div');
                label.style.position = 'absolute';
                label.style.top = '-20px';
                label.style.left = '50%';
                label.style.transform = 'translateX(-50%)';
                label.style.backgroundColor = 'rgba(0,0,0,0.7)';
                label.style.color = 'white';
                label.style.padding = '2px 6px';
                label.style.borderRadius = '3px';
                label.style.fontSize = '10px';
                label.style.whiteSpace = 'nowrap';
                label.style.zIndex = '1001';
                label.textContent = point.label;
                
                marker.appendChild(label);
                canvasClone.appendChild(marker);
            });
            
            // 临时将克隆的元素添加到DOM并隐藏
            canvasClone.style.position = 'absolute';
            canvasClone.style.top = '-9999px';
            canvasClone.style.left = '-9999px';
            document.body.appendChild(canvasClone);
            
            // 使用html2canvas将DOM元素转换为canvas
            html2canvas(canvasClone, {
                backgroundColor: 'white',
                scale: 1,
                logging: true,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const imageDataURL = canvas.toDataURL('image/png');
                sessionStorage.setItem('temp_circuit_png', imageDataURL);
                // console.log('电路图已保存到sessionStorage');
                
                // 清理DOM
                if (document.body.contains(canvasClone)) {
                    document.body.removeChild(canvasClone);
                }
                if (document.body.contains(loadingIndicator)) {
                    document.body.removeChild(loadingIndicator);
                }
                
                // 刷新实验环境窗口
                if (experimentWindow) {
                    experimentWindow.location.reload();
                }
            }).catch(error => {
                console.error('生成电路图失败:', error);
                if (document.body.contains(canvasClone)) {
                    document.body.removeChild(canvasClone);
                }
                if (document.body.contains(loadingIndicator)) {
                    document.body.removeChild(loadingIndicator);
                }
            });
          } catch (error) {
              console.error('处理电路图失败:', error);
              if (document.body.contains(loadingIndicator)) {
                  document.body.removeChild(loadingIndicator);
              }
          }
      }
    } catch (error) {
      console.error('❌ 跳转实验环境失败:', error);
      ElMessage.error('跳转实验环境失败，请重试');
    }
  };

  /**
   * 调试函数区域 - 已注释，需要时取消注释
   */
  // 显示/隐藏连接点
  const toggleConnectionPoints = () => {
    console.log('👁️ ========== 显示/隐藏连接点 ==========');

    try {
      // 移除现有的连接点标记
      const existingMarkers = document.querySelectorAll('.connection-point-marker');
      if (existingMarkers.length > 0) {
        existingMarkers.forEach(marker => marker.remove());
        console.log('🔴 隐藏连接点');
        return;
      }

      // 显示连接点
      console.log('🟢 显示连接点');

      components.value.forEach((component, compIndex) => {
        if (component.connectionPoints) {
          component.connectionPoints.forEach((cp, cpIndex) => {
            if (cp.point) {
              // 创建连接点标记
              const marker = document.createElement('div');
              marker.className = 'connection-point-marker';
              marker.style.position = 'absolute';
              marker.style.left = `${cp.point.x - 4}px`;
              marker.style.top = `${cp.point.y - 4}px`;
              marker.style.width = '8px';
              marker.style.height = '8px';
              marker.style.backgroundColor = cp.polarity === 'positive' ? '#ff4444' :
                                           cp.polarity === 'negative' ? '#4444ff' : '#44ff44';
              marker.style.borderRadius = '50%';
              marker.style.border = '2px solid white';
              marker.style.boxShadow = '0 0 4px rgba(0,0,0,0.5)';
              marker.style.zIndex = '9999';
              marker.style.pointerEvents = 'none';

              // 添加标签
              const label = document.createElement('div');
              label.style.position = 'absolute';
              label.style.top = '-20px';
              label.style.left = '50%';
              label.style.transform = 'translateX(-50%)';
              label.style.backgroundColor = 'rgba(0,0,0,0.8)';
              label.style.color = 'white';
              label.style.padding = '2px 4px';
              label.style.borderRadius = '3px';
              label.style.fontSize = '10px';
              label.style.whiteSpace = 'nowrap';
              label.style.zIndex = '10000';
              label.textContent = `${component.symbol || component.label}-${cp.id}`;

              marker.appendChild(label);

              // 添加到画布
              const canvas = document.getElementById('canvas-area') || document.body;
              canvas.appendChild(marker);

              console.log(`📍 ${component.label} 连接点${cp.id}: (${cp.point.x}, ${cp.point.y}) ${cp.polarity || ''}`);
            }
          });
        }
      });

    } catch (error) {
      console.error('❌ 显示连接点时出错:', error);
    }

    console.log('👁️ ========== 完成 ==========');
  };

  // 调试电源组件按钮
  const debugPowerComponent = () => {
    console.log('⚡ ========== 调试电源组件 ==========');

    try {
      // 查找所有电源组件
      const powerComponents = components.value.filter(comp =>
        comp.type === 'voltage_source' ||
        comp.label === '直流电压源' ||
        comp.symbol === 'Ucc'
      );

      console.log(`📊 找到 ${powerComponents.length} 个电源组件:`);

      powerComponents.forEach((comp, index) => {
        console.log(`🔋 电源组件 ${index + 1}:`, {
          id: comp.id,
          type: comp.type,
          label: comp.label,
          symbol: comp.symbol,
          identifier: comp.identifier,
          value: comp.value,
          unit: comp.unit,
          x: comp.x,
          y: comp.y,
          connectionPoints: comp.connectionPoints?.map(cp => ({
            id: cp.id,
            x: cp.x,
            y: cp.y,
            point: cp.point,
            polarity: cp.polarity
          }))
        });

        // 检查连接点坐标
        if (comp.connectionPoints) {
          comp.connectionPoints.forEach((cp, cpIndex) => {
            console.log(`  连接点 ${cpIndex + 1}:`, {
              id: cp.id,
              原始坐标: `(${cp.x}, ${cp.y})`,
              计算后坐标: cp.point ? `(${cp.point.x}, ${cp.point.y})` : '未计算',
              极性: cp.polarity
            });
          });
        }
      });

      // 检查是否有连接到电源的线段
      console.log('🔍 检查连接到电源的线段...');
      const powerConnections = [];

      if (powerComponents.length > 0) {
        const powerComp = powerComponents[0];
        if (powerComp.connectionPoints) {
          powerComp.connectionPoints.forEach(cp => {
            if (cp.point) {
              const pointKey = `${cp.point.x},${cp.point.y}`;
              console.log(`检查电源连接点: ${pointKey}`);

              // 检查是否有其他组件连接到这个点
              components.value.forEach(otherComp => {
                if (otherComp.id !== powerComp.id && otherComp.connectionPoints) {
                  otherComp.connectionPoints.forEach(otherCp => {
                    if (otherCp.point) {
                      const otherPointKey = `${otherCp.point.x},${otherCp.point.y}`;
                      if (pointKey === otherPointKey) {
                        powerConnections.push({
                          power: powerComp,
                          powerPoint: cp,
                          connected: otherComp,
                          connectedPoint: otherCp
                        });
                      }
                    }
                  });
                }
              });
            }
          });
        }
      }

      console.log(`📊 电源直接连接: ${powerConnections.length} 个`);
      powerConnections.forEach((conn, index) => {
        console.log(`  连接 ${index + 1}: ${conn.power.label} → ${conn.connected.label}`);
      });

    } catch (error) {
      console.error('❌ 调试电源组件时出错:', error);
    }

    console.log('⚡ ========== 调试完成 ==========');
  };

  // 调试用户连接关系按钮
  const debugUserConnections = () => {
    console.log('🔗 ========== 调试用户连接关系 ==========');

    try {
      // 生成用户连接关系
      const userConnections = generateConnectionRelationships();

      console.log('📋 用户连接关系JSON:');
      console.log(JSON.stringify(userConnections, null, 2));

      console.log('📊 连接关系统计:');
      console.log(`总连接数: ${userConnections.length}`);

      // 统计组件类型
      const componentTypes = new Set();
      userConnections.forEach(conn => {
        const fromType = extractComponentType(conn.from);
        const toType = extractComponentType(conn.to);
        componentTypes.add(fromType);
        componentTypes.add(toType);
      });

      console.log(`涉及组件类型: ${Array.from(componentTypes).join(', ')}`);

      // 生成拓扑结构
      const topology = buildTopology(userConnections);
      console.log('📊 用户拓扑结构:');
      console.log(topology);

    } catch (error) {
      console.error('❌ 生成用户连接关系时出错:', error);
    }

    console.log('🔗 ========== 调试完成 ==========');
  };

  // 调试测点关系按钮
  const debugTestPointRelations = () => {
    console.log('🔧 ========== 调试测点关系 ==========');

    // 🔧 首先检查连接关系
    const userConnections = generateConnectionRelationships();
    console.log('🔧 当前连接关系:', {
      connectionsCount: userConnections?.length || 0,
      connections: userConnections
    });

    // 🔧 移除测点计数器重置，现在使用组件ID作为标识符
    // window.testPointCounter = 0;

    // 获取所有测点组件
    const testPoints = []
    components.value?.forEach(component => {
      if (component.type === 'testPoint' || component.isTestPoint) {
        // 🔧 添加调试日志 - 测点组件详情
        console.log('🔧 发现测点组件:', {
          id: component.id || component.componentId,
          identifier: component.identifier,
          type: component.type,
          hasIdentifier: !!component.identifier,
          identifierType: typeof component.identifier,
          fullComponent: component
        });

        testPoints.push({
          id: component.id || component.componentId,
          identifier: component.identifier || '',
          x: component.x,
          y: component.y,
          component: component
        })
      }
    })

    console.log(`📍 找到 ${testPoints.length} 个测点`);

    if (testPoints.length === 0) {
      console.log('❌ 未找到任何测点组件');
      ElMessage({
        message: '未找到任何测点组件',
        type: 'warning',
        duration: 3000
      });
      return;
    }

    // 生成测点分组数据
    const testPointGroups = {}
    let testPointCounter = 0

    testPoints.forEach((testPoint) => {
      try {
        // 🔧 添加调试信息
        console.log('🔧 正在分析测点:', {
          testPoint: testPoint,
          component: testPoint.component,
          componentsCount: components.value?.length || 0
        });

        // 调用函数获取周围组件
        const surroundingComponents = findSurroundingComponents(testPoint.component, components.value);

        console.log('🔧 findSurroundingComponents 返回结果:', {
          surroundingComponentsCount: surroundingComponents.length,
          surroundingComponents: surroundingComponents
        });

        if (surroundingComponents.length > 0) {
          testPointCounter++
          const groupId = testPointCounter.toString()

          testPointGroups[groupId] = {
            connectedDevices: surroundingComponents.map(comp => ({
              typeName: comp.typeName,
              identifier: comp.identifier
            }))
          }

          // 简洁的单行输出
          const deviceList = surroundingComponents.map(comp => comp.typeName).join('、')
          console.log(`${groupId}测点: ${deviceList} (${surroundingComponents.length}个器件)`)
        } else {
          console.log('🔧 测点没有找到周围组件');
        }
      } catch (error) {
        console.error(`测点分析出错:`, error);
      }
    });

    // 输出最终的JSON格式
    console.log('📊 测点分组JSON数据:');
    console.log(JSON.stringify(testPointGroups, null, 2));

    ElMessage({
      message: `🔧 找到 ${Object.keys(testPointGroups).length} 个测点分组，查看控制台JSON数据`,
      type: 'success',
      duration: 3000
    });

    console.log('🔧 ========== 调试完成 ==========');

    // 跳过原有的详细调试逻辑
    return;
    
    // 创建测点位置索引 - 通过坐标查找测点
    const testPointsByPosition = new Map();
    testPoints.forEach(tp => {
      // 使用网格坐标作为键
      const gridX = Math.round(tp.x / 20) * 20; // 假设网格大小为20
      const gridY = Math.round(tp.y / 20) * 20;
      const posKey = `${gridX},${gridY}`;
      testPointsByPosition.set(posKey, tp);
    });

    // 创建组件位置映射 - 用于后续按位置查找组件
    const componentPositionMap = new Map();
    // 创建组件类型索引 - 按类型分组组件
    const componentsByType = new Map();
    
    components.value?.forEach(component => {
      if (component.type !== 'testPoint' && !component.isTestPoint) {
        // 计算组件中心点并映射到网格
        const gridX = Math.round(component.x / 20) * 20;
        const gridY = Math.round(component.y / 20) * 20;
        const posKey = `${gridX},${gridY}`;
        
        // 添加到位置映射
        if (!componentPositionMap.has(posKey)) {
          componentPositionMap.set(posKey, []);
        }
        
        const componentInfo = {
          id: component.id || component.componentId,
          type: component.type,
          baseType: getBaseComponentType(component.type),
          label: component.label,
          identifier: component.identifier,
          value: component.value,
          unit: component.unit,
          x: component.x,
          y: component.y,
          gridX,
          gridY
        };
        
        componentPositionMap.get(posKey).push(componentInfo);
        
        // 添加到类型索引
        const baseType = getBaseComponentType(component.type);
        if (!componentsByType.has(baseType)) {
          componentsByType.set(baseType, []);
        }
        componentsByType.get(baseType).push(componentInfo);
      }
    });
    
    // 标准化组件标识符 - 为每种类型的组件分配顺序标识符
    componentsByType.forEach((components, type) => {
      // 按x坐标排序，从左到右分配编号
      components.sort((a, b) => a.x - b.x);
      
      // 分配标准化标识符
      components.forEach((comp, index) => {
        // 如果组件已有标识符，优先使用原始标识符
        if (comp.identifier && /^[A-Za-z][0-9]+$/.test(comp.identifier)) {
          comp.standardIdentifier = comp.identifier;
          console.log(`保留原始标识符: ${comp.identifier}`);
        } else {
          const prefix = getStandardPrefix(type);
          comp.standardIdentifier = `${prefix}${index + 1}`;
          console.log(`分配标准化标识符: ${comp.identifier || '未命名'} -> ${comp.standardIdentifier}`);
        }
      });
    });
    
    // 提取坐标信息的辅助函数
    const extractCoordinates = (desc) => {
      // 尝试匹配描述中的坐标信息，格式可能是 "x:100,y:200" 或 "(100,200)" 等
      const coordMatch = desc.match(/x:(\d+),\s*y:(\d+)/) || 
                        desc.match(/\((\d+),\s*(\d+)\)/) ||
                        desc.match(/坐标[：:]\s*(\d+),\s*(\d+)/);
      
      if (coordMatch) {
        return {
          x: parseInt(coordMatch[1]),
          y: parseInt(coordMatch[2])
        };
      }
      return null;
    };

    // 提取组件信息的辅助函数
    const extractComponentInfo = (desc) => {
      // 提取器件类型、标识符和位置
      let componentIdentifier = '';
      let componentValue = '';
      let componentUnit = '';
      let coords = extractCoordinates(desc);
      
      // 使用器件库数据识别组件类型
      const componentData = identifyComponentTypeFromLibrary(desc);
      let componentType = componentData.type;
      
      // 尝试从描述中识别标识符
      const identifierMatch = desc.match(/[A-Za-z][0-9]+/g);
      if (identifierMatch) {
        // 如果有多个匹配，选择与组件类型匹配的
        const typedIdentifiers = identifierMatch.filter(id => {
          const prefix = id.match(/^[A-Za-z]+/)[0];
          const lowerPrefix = prefix.toLowerCase();
          
          return (componentType === '电感' && (lowerPrefix === 'l')) ||
                // 更精确区分不同类型的电容
                (componentType === '无极性电容' && (lowerPrefix === 'c' || lowerPrefix === 'nc')) ||
                (componentType === '极性电容' && (lowerPrefix === 'c' || lowerPrefix === 'pc')) ||
                (componentType === '可变电容' && (lowerPrefix === 'c' || lowerPrefix === 'vc')) ||
                (componentType === '电容' && (lowerPrefix === 'c')) ||
                (componentType === '电阻' && (lowerPrefix === 'r')) ||
                (componentType === '可变电阻' && (lowerPrefix === 'r' || lowerPrefix === 'vr')) ||
                (componentType === '二极管' && (lowerPrefix === 'd')) ||
                (componentType === '单向稳压管' && (lowerPrefix === 'd' || lowerPrefix === 'zd')) ||
                (componentType === '双向稳压管' && (lowerPrefix === 'd' || lowerPrefix === 'dzd')) ||
                (componentType === '发光二极管' && (lowerPrefix === 'd' || lowerPrefix === 'led')) ||
                (componentType === '三极管' && (lowerPrefix === 'q' || lowerPrefix === 't')) ||
                (componentType === 'PNP三极管' && (lowerPrefix === 'q' || lowerPrefix === 't')) ||
                (componentType === 'NPN三极管' && (lowerPrefix === 'q' || lowerPrefix === 't')) ||
                (componentType === '单刀单掷开关' && (lowerPrefix === 's')) ||
                (componentType === '单刀双掷开关' && (lowerPrefix === 's' || lowerPrefix === 'spdt')) ||
                (componentType === '开关' && (lowerPrefix === 's' || lowerPrefix === 'sw')) ||
                (componentType === '按钮' && (lowerPrefix === 's' || lowerPrefix === 'btn' || lowerPrefix === 'pb')) ||
                (componentType === '继电器' && (lowerPrefix === 'rl' || lowerPrefix === 'k')) ||
                (componentType === '变压器' && (lowerPrefix === 't' || lowerPrefix === 'tr')) ||
                (componentType === '电压表' && (lowerPrefix === 'v')) ||
                (componentType === '电流表' && (lowerPrefix === 'a')) ||
                (componentType === '万用表' && (lowerPrefix === 'm')) ||
                (componentType === '示波器' && (lowerPrefix === 'osc')) ||
                (componentType === '信号发生器' && (lowerPrefix === 'g' || lowerPrefix === 'gen')) ||
                (componentType === '测点' && (lowerPrefix === 'tp')) ||
                (componentType === '晶体管' && (lowerPrefix === 'y' || lowerPrefix === 'x')) ||
                (componentType === '直流电压源' && (lowerPrefix === 'ps' || lowerPrefix === 'v' || lowerPrefix === 'e' || lowerPrefix === 'b')) ||
                (componentType === '电源' && (lowerPrefix === 'ps' || lowerPrefix === 'v' || lowerPrefix === 'e' || lowerPrefix === 'b')) ||
                (componentType === '电机' && (lowerPrefix === 'm')) ||
                (componentType === '保险管' && (lowerPrefix === 'f')) ||
                (componentType === '接地' && (lowerPrefix === 'gnd')) ||
                (componentType === '天线' && (lowerPrefix === 'ant')) ||
                (componentType === '扬声器' && (lowerPrefix === 'sp')) ||
                (componentType === '麦克风' && (lowerPrefix === 'mic')) ||
                (componentType === '三端稳压集成电路' && (lowerPrefix === 'u' || lowerPrefix === 'ic')) ||
                (componentType === '运算放大器' && (lowerPrefix === 'u' || lowerPrefix === 'op')) ||
                (componentType === '逻辑门' && (lowerPrefix === 'u' || lowerPrefix === 'ic')) ||
                (componentType === '集成电路' && (lowerPrefix === 'u' || lowerPrefix === 'ic'));
        });
        
        if (typedIdentifiers.length > 0) {
          componentIdentifier = typedIdentifiers[0];
        } else if (identifierMatch.length > 0) {
          componentIdentifier = identifierMatch[0];
        }
      }
      
      // 尝试提取值和单位
      const valueUnitMatch = desc.match(/\s*-\s*([0-9.]+)\s*([kMnpµ]?[FΩVHHz]*)/);
      if (valueUnitMatch) {
        componentValue = valueUnitMatch[1];
        componentUnit = valueUnitMatch[2];
      } else {
        // 尝试其他格式
        const altValueMatch = desc.match(/([0-9.]+)\s*([kMnpµ]?[FΩVHHz]+)/);
        if (altValueMatch) {
          componentValue = altValueMatch[1];
          componentUnit = altValueMatch[2];
        }
      }
      
      return {
        type: componentType,
        identifier: componentIdentifier,
        value: componentValue,
        unit: componentUnit,
        coordinates: coords
      };
    };
    
    // 查找组件的标准化标识符
    const findStandardIdentifier = (componentInfo) => {
      // 如果没有坐标信息，无法查找
      if (!componentInfo.coordinates) return null;
      
      // 转换为网格坐标
      const gridX = Math.round(componentInfo.coordinates.x / 20) * 20;
      const gridY = Math.round(componentInfo.coordinates.y / 20) * 20;
      const posKey = `${gridX},${gridY}`;
      
      // 查找位置匹配的组件
      if (componentPositionMap.has(posKey)) {
        const componentsAtPosition = componentPositionMap.get(posKey);
        
        // 按类型过滤
        const sameTypeComponents = componentsAtPosition.filter(c => 
          getBaseComponentType(c.type) === getBaseComponentType(componentInfo.type) ||
          c.type.includes(componentInfo.type.toLowerCase())
        );
        
        if (sameTypeComponents.length > 0) {
          // 返回标准化标识符
          return sameTypeComponents[0].standardIdentifier;
        }
      }
      
      // 如果没有找到位置匹配的组件，尝试通过类型查找
      const baseType = getBaseComponentType(componentInfo.type);
      if (componentsByType.has(baseType)) {
        const componentsOfType = componentsByType.get(baseType);
        
        // 如果有原始标识符，尝试匹配
        if (componentInfo.identifier) {
          const prefix = componentInfo.identifier.match(/^[A-Za-z]+/);
          const number = componentInfo.identifier.match(/[0-9]+$/);
          
          if (prefix && number) {
            const index = parseInt(number[0]) - 1;
            if (index >= 0 && index < componentsOfType.length) {
              return componentsOfType[index].standardIdentifier;
            }
          }
        }
        
        // 如果没有找到匹配，使用第一个组件的标准标识符
        if (componentsOfType.length > 0) {
          return componentsOfType[0].standardIdentifier;
        }
      }
      
      return null;
    };
    
    // 处理每个连接
    connections.forEach(connection => {
      const fromDesc = connection.from || '';
      const toDesc = connection.to || '';
      
      console.log('分析连接:', fromDesc, ' -> ', toDesc);
      
      // 查找from端是否是测点
      let fromTestPoint = null;
        for (const tp of testPoints) {
        if (fromDesc.includes('测点') && 
            (fromDesc.includes(tp.id) || 
              (tp.identifier && fromDesc.includes(tp.identifier)) ||
              fromDesc.includes(`测点 - ${tp.identifier}`) ||
              fromDesc.includes(`测点-${tp.identifier}`) ||
             fromDesc.includes(`测点 -  ${tp.identifier}`))) {
            fromTestPoint = tp;
            console.log(`  from端确认为测点${tp.index}: ${tp.identifier}`);
            break;
        }
      }
      
      // 查找to端是否是测点
      let toTestPoint = null;
        for (const tp of testPoints) {
        if (toDesc.includes('测点') && 
            (toDesc.includes(tp.id) || 
              (tp.identifier && toDesc.includes(tp.identifier)) ||
              toDesc.includes(`测点 - ${tp.identifier}`) ||
              toDesc.includes(`测点-${tp.identifier}`) ||
             toDesc.includes(`测点 -  ${tp.identifier}`))) {
            toTestPoint = tp;
            console.log(`  to端确认为测点${tp.index}: ${tp.identifier}`);
            break;
        }
      }
      
      // 如果通过描述没找到测点，尝试通过坐标查找
      if (!fromTestPoint) {
        const coords = extractCoordinates(fromDesc);
        if (coords) {
          // 转换为网格坐标
          const gridX = Math.round(coords.x / 20) * 20;
          const gridY = Math.round(coords.y / 20) * 20;
          const posKey = `${gridX},${gridY}`;
          
          if (testPointsByPosition.has(posKey)) {
            fromTestPoint = testPointsByPosition.get(posKey);
            console.log(`  通过坐标(${gridX},${gridY})找到from端测点: ${fromTestPoint.identifier}`);
          }
        }
      }
      
      if (!toTestPoint) {
        const coords = extractCoordinates(toDesc);
        if (coords) {
          // 转换为网格坐标
          const gridX = Math.round(coords.x / 20) * 20;
          const gridY = Math.round(coords.y / 20) * 20;
          const posKey = `${gridX},${gridY}`;
          
          if (testPointsByPosition.has(posKey)) {
            toTestPoint = testPointsByPosition.get(posKey);
            console.log(`  通过坐标(${gridX},${gridY})找到to端测点: ${toTestPoint.identifier}`);
          }
        }
      }
      
      // 处理from端是测点的情况
      if (fromTestPoint) {
        if (toTestPoint) {
          // 测点到测点的连接
          const connectedType = `测点 ${toTestPoint.identifier}`;
          console.log(`  测点${fromTestPoint.index}(${fromTestPoint.identifier})连接到测点${toTestPoint.index}(${toTestPoint.identifier})`);
          
          fromTestPoint.connections.push({
            type: connectedType,
            identifier: toTestPoint.identifier,
            isTestPoint: true
          });
        } else {
          // 测点连接到其他器件
          const componentInfo = extractComponentInfo(toDesc);
          
          if (componentInfo.type !== '未知器件') {
            // 查找标准化标识符或使用原始标识符
            let standardIdentifier = null;
            // 如果组件描述中已包含标识符(如C1, R2等)，优先使用该标识符
            if (componentInfo.identifier && /^[A-Za-z][0-9]+$/.test(componentInfo.identifier)) {
              standardIdentifier = componentInfo.identifier;
              console.log(`  使用描述中的标识符: ${standardIdentifier}`);
            } else {
              standardIdentifier = findStandardIdentifier(componentInfo);
              console.log(`  查找标准标识符: ${standardIdentifier || '未找到'}`);
            }
            
            // 生成更详细的连接描述
            let detailedType = componentInfo.type;
            
            // 如果有标识符，添加到类型中
            if (standardIdentifier) {
              detailedType += ` (${standardIdentifier})`;
              componentInfo.standardIdentifier = standardIdentifier;
            } else if (componentInfo.identifier) {
              detailedType += ` (${componentInfo.identifier})`;
            }
            
            // 如果有值和单位，添加到类型中
            if (componentInfo.value && componentInfo.unit) {
              detailedType += ` ${componentInfo.value}${componentInfo.unit}`;
            }
            
            console.log(`  测点${fromTestPoint.index}(${fromTestPoint.identifier})连接到器件: ${detailedType}`);
            
          fromTestPoint.connections.push({
              type: componentInfo.type,
              identifier: componentInfo.identifier,
              standardIdentifier: componentInfo.standardIdentifier,
              value: componentInfo.value,
              unit: componentInfo.unit,
              detail: detailedType,
              originalDesc: toDesc
            });
          }
        }
      }
      
      // 处理to端是测点的情况
      if (toTestPoint) {
        if (!fromTestPoint) { // 避免重复添加测点到测点的连接
          // 其他器件连接到测点
          const componentInfo = extractComponentInfo(fromDesc);
          
          if (componentInfo.type !== '未知器件') {
            // 查找标准化标识符或使用原始标识符
            let standardIdentifier = null;
            // 如果组件描述中已包含标识符(如C1, R2等)，优先使用该标识符
            if (componentInfo.identifier && /^[A-Za-z][0-9]+$/.test(componentInfo.identifier)) {
              standardIdentifier = componentInfo.identifier;
              console.log(`  使用描述中的标识符: ${standardIdentifier}`);
        } else {
              standardIdentifier = findStandardIdentifier(componentInfo);
              console.log(`  查找标准标识符: ${standardIdentifier || '未找到'}`);
            }
            
            // 生成更详细的连接描述
            let detailedType = componentInfo.type;
            
            // 如果有标识符，添加到类型中
            if (standardIdentifier) {
              detailedType += ` (${standardIdentifier})`;
              componentInfo.standardIdentifier = standardIdentifier;
            } else if (componentInfo.identifier) {
              detailedType += ` (${componentInfo.identifier})`;
            }
            
            // 如果有值和单位，添加到类型中
            if (componentInfo.value && componentInfo.unit) {
              detailedType += ` ${componentInfo.value}${componentInfo.unit}`;
            }
            
            console.log(`  测点${toTestPoint.index}(${toTestPoint.identifier})连接到器件: ${detailedType}`);
            
            toTestPoint.connections.push({
              type: componentInfo.type,
              identifier: componentInfo.identifier,
              standardIdentifier: componentInfo.standardIdentifier,
              value: componentInfo.value,
              unit: componentInfo.unit,
              detail: detailedType,
              originalDesc: fromDesc
            });
          } else {
            console.log(`  测点${toTestPoint.index}(${toTestPoint.identifier})连接到未知器件`);
          }
        }
      }
    });
    
    // 主动扫描所有可能的连接
    testPoints.forEach(testPoint => {
      console.log(`主动扫描测点${testPoint.index}(${testPoint.identifier})的连接...`);
      
      // 获取测点坐标
      const testPointX = testPoint.x;
      const testPointY = testPoint.y;
      
      // 存储已确认的直接连接组件ID，避免重复添加
      const confirmedConnections = new Set();
      testPoint.connections.forEach(conn => {
        if (conn.standardIdentifier) {
          confirmedConnections.add(conn.standardIdentifier);
        } else if (conn.identifier) {
          confirmedConnections.add(conn.identifier);
        }
      });
      
      // 特殊处理 - 提前检查所有组件，看是否有非常接近测点的
      console.log(`  预检查直接相邻的组件...`);
      componentsByType.forEach((components, type) => {
        components.forEach(comp => {
          const distToComponent = Math.sqrt(
            Math.pow(comp.x - testPointX, 2) + 
            Math.pow(comp.y - testPointY, 2)
          );
          
          // 对于非常接近的组件直接添加连接
          if (distToComponent < 40) { // 增加到40像素以内的组件视为直接连接
            const compId = comp.standardIdentifier || comp.identifier;
            
            if (!confirmedConnections.has(compId)) {
              console.log(`  发现直接相邻组件: ${comp.type}(${compId})，距离=${Math.round(distToComponent)}px`);
              
              testPoint.connections.push({
                type: comp.type,
                identifier: comp.identifier || '',
                standardIdentifier: comp.standardIdentifier || '',
                value: comp.value || '',
                unit: comp.unit || '',
                detail: `${comp.type} (${compId})${comp.value ? ' ' + comp.value + comp.unit : ''}`,
                originalDesc: `直接相邻组件`,
                proximity: Math.round(distToComponent),
                connectionType: 'direct',
                isOnLine: true
              });
              
              confirmedConnections.add(compId);
            }
          }
        });
      });
      
      // 1. 首先分析所有连接，查找测点参与的连接
      const testPointConnections = [];
      connections.forEach(conn => {
        // 提取坐标
        const fromCoords = extractCoordinates(conn.from);
        const toCoords = extractCoordinates(conn.to);
        
        if (!fromCoords || !toCoords) return; // 跳过没有坐标的连接
        
        // 计算与测点的距离
        const distFromToTestPoint = Math.sqrt(
          Math.pow(fromCoords.x - testPointX, 2) + 
          Math.pow(fromCoords.y - testPointY, 2)
        );
        
        const distToToTestPoint = Math.sqrt(
          Math.pow(toCoords.x - testPointX, 2) + 
          Math.pow(toCoords.y - testPointY, 2)
        );
        
        const DIRECT_CONNECTION_THRESHOLD = 40; // 缩小阈值为40px
        
        // 添加连线判断 - 检查测点是否真的在连线上
        const isOnLine = isPointOnLine(
          testPointX, testPointY,
          fromCoords.x, fromCoords.y,
          toCoords.x, toCoords.y
        );
        
        console.log(`  检查连接 ${conn.from} -> ${conn.to}`);
        console.log(`    测点到起点距离: ${Math.round(distFromToTestPoint)}px`);
        console.log(`    测点到终点距离: ${Math.round(distToToTestPoint)}px`);
        console.log(`    测点是否在连线上: ${isOnLine ? '是' : '否'}`);
        
        // 只有当测点在连线上或非常接近连接点时才认为是连接
        if (isOnLine || 
            distFromToTestPoint < DIRECT_CONNECTION_THRESHOLD || 
            distToToTestPoint < DIRECT_CONNECTION_THRESHOLD) {
          let otherEndCoords, otherEndDesc;
          
          // 优先使用距离较远的端点作为连接的另一端
          if (distFromToTestPoint < distToToTestPoint) {
            // from端接近测点，另一端是to
            otherEndCoords = toCoords;
            otherEndDesc = conn.to;
            console.log(`  确认测点连接: 距离=${Math.round(distFromToTestPoint)}px，另一端=${conn.to}`);
          } else {
            // to端接近测点，另一端是from
            otherEndCoords = fromCoords;
            otherEndDesc = conn.from;
            console.log(`  确认测点连接: 距离=${Math.round(distToToTestPoint)}px，另一端=${conn.from}`);
          }
          
          testPointConnections.push({
            distance: Math.min(distFromToTestPoint, distToToTestPoint),
            otherEndCoords,
            otherEndDesc,
            isOnLine: isOnLine
          });
        }
      });
      
      // 2. 查找连接另一端对应的组件
      console.log(`  找到${testPointConnections.length}个可能的测点连接`);
      console.log(`  其中在连线上的连接: ${testPointConnections.filter(c => c.isOnLine).length}个`);

      // 优先处理在连线上的连接
      const onLineConnections = testPointConnections.filter(c => c.isOnLine);
      const proximityConnections = testPointConnections.filter(c => !c.isOnLine);

      // 处理在连线上的连接
      onLineConnections.forEach(conn => {
        // 查找连接另一端的组件
        const componentInfo = extractComponentInfo(conn.otherEndDesc);
        
        if (componentInfo.type === '未知器件') {
          console.log(`  无法识别组件类型: ${conn.otherEndDesc}`);
          
          // 尝试通过坐标查找组件
          let bestComponent = null;
          let minDistance = Infinity;
          
          componentsByType.forEach((components, type) => {
            components.forEach(comp => {
              const distToComp = Math.sqrt(
                Math.pow(comp.x - conn.otherEndCoords.x, 2) + 
                Math.pow(comp.y - conn.otherEndCoords.y, 2)
              );
              
              if (distToComp < 50 && distToComp < minDistance) { // 50px阈值
                minDistance = distToComp;
                bestComponent = comp;
              }
            });
          });
          
          if (bestComponent) {
            console.log(`  通过坐标找到组件: ${bestComponent.type}(${bestComponent.standardIdentifier || bestComponent.identifier}), 距离=${Math.round(minDistance)}px`);
            
            const compId = bestComponent.standardIdentifier || bestComponent.identifier;
            
            // 检查是否已添加
            if (!confirmedConnections.has(compId)) {
              console.log(`  添加连线上的连接: 测点${testPoint.identifier} -> ${bestComponent.type}(${compId})`);
              
              testPoint.connections.push({
                type: bestComponent.type,
                identifier: bestComponent.identifier || '',
                standardIdentifier: bestComponent.standardIdentifier || '',
                value: bestComponent.value || '',
                unit: bestComponent.unit || '',
                detail: `${bestComponent.type} (${compId})${bestComponent.value ? ' ' + bestComponent.value + bestComponent.unit : ''}`,
                originalDesc: conn.otherEndDesc,
                proximity: Math.round(minDistance),
                connectionType: 'direct',
                isOnLine: true
              });
              
              confirmedConnections.add(compId);
            }
          }
        } else {
          // 找到组件标准标识符
          const standardIdentifier = findStandardIdentifier(componentInfo);
          if (standardIdentifier) componentInfo.standardIdentifier = standardIdentifier;
          
          const compId = componentInfo.standardIdentifier || componentInfo.identifier;
          
          // 检查是否已添加
          if (!confirmedConnections.has(compId)) {
            console.log(`  添加连线上的连接: 测点${testPoint.identifier} -> ${componentInfo.type}(${compId})`);
            
            testPoint.connections.push({
              type: componentInfo.type,
              identifier: componentInfo.identifier || '',
              standardIdentifier: componentInfo.standardIdentifier || '',
              value: componentInfo.value || '',
              unit: componentInfo.unit || '',
              detail: `${componentInfo.type} (${compId})${componentInfo.value ? ' ' + componentInfo.value + componentInfo.unit : ''}`,
              originalDesc: conn.otherEndDesc,
              proximity: Math.round(conn.distance),
              connectionType: 'direct',
              isOnLine: true
            });
            
            confirmedConnections.add(compId);
          }
        }
      });
      
      // 如果连线上的连接不足，再处理接近连接点的连接
      if (testPoint.connections.length < 2) {
        proximityConnections.forEach(conn => {
          // 查找连接另一端的组件
          const componentInfo = extractComponentInfo(conn.otherEndDesc);
          
          if (componentInfo.type === '未知器件') {
            console.log(`  无法识别组件类型: ${conn.otherEndDesc}`);
            
            // 尝试通过坐标查找组件
            let bestComponent = null;
            let minDistance = Infinity;
            
            componentsByType.forEach((components, type) => {
              components.forEach(comp => {
                const distToComp = Math.sqrt(
                  Math.pow(comp.x - conn.otherEndCoords.x, 2) + 
                  Math.pow(comp.y - conn.otherEndCoords.y, 2)
                );
                
                if (distToComp < 50 && distToComp < minDistance) { // 50px阈值
                  minDistance = distToComp;
                  bestComponent = comp;
                }
              });
            });
            
            if (bestComponent) {
              console.log(`  通过坐标找到组件: ${bestComponent.type}(${bestComponent.standardIdentifier || bestComponent.identifier}), 距离=${Math.round(minDistance)}px`);
              
              const compId = bestComponent.standardIdentifier || bestComponent.identifier;
              
              // 检查是否已添加
              if (!confirmedConnections.has(compId)) {
                console.log(`  添加接近组件: 测点${testPoint.identifier} -> ${bestComponent.type}(${compId})，距离=${Math.round(minDistance)}px`);
                
                testPoint.connections.push({
                  type: bestComponent.type,
                  identifier: bestComponent.identifier || '',
                  standardIdentifier: bestComponent.standardIdentifier || '',
                  value: bestComponent.value || '',
                  unit: bestComponent.unit || '',
                  detail: `${bestComponent.type} (${compId})${bestComponent.value ? ' ' + bestComponent.value + bestComponent.unit : ''}`,
                  originalDesc: conn.otherEndDesc,
                  proximity: Math.round(minDistance),
                  connectionType: 'proximity',
                  isOnLine: false
                });
                
                confirmedConnections.add(compId);
              }
            }
          } else {
            // 找到组件标准标识符
            const standardIdentifier = findStandardIdentifier(componentInfo);
            if (standardIdentifier) componentInfo.standardIdentifier = standardIdentifier;
            
            const compId = componentInfo.standardIdentifier || componentInfo.identifier;
            
            // 检查是否已添加
            if (!confirmedConnections.has(compId)) {
              console.log(`  添加接近组件: 测点${testPoint.identifier} -> ${componentInfo.type}(${compId})，距离=${Math.round(conn.distance)}px`);
              
              testPoint.connections.push({
                type: componentInfo.type,
                identifier: componentInfo.identifier || '',
                standardIdentifier: componentInfo.standardIdentifier || '',
                value: componentInfo.value || '',
                unit: componentInfo.unit || '',
                detail: `${componentInfo.type} (${compId})${componentInfo.value ? ' ' + componentInfo.value + componentInfo.unit : ''}`,
                originalDesc: conn.otherEndDesc,
                proximity: Math.round(conn.distance),
                connectionType: 'proximity',
                isOnLine: false
              });
              
              confirmedConnections.add(compId);
            }
          }
        });
      }
      
      // 4. 特殊情况：参考连接关系检查
      if (testPoint.connections.length < 2) {
        // 检查是否有从R1到D1的连接，如果有，那么测点可能在连线上
        const hasR1ToD1Connection = connections.some(conn => {
          const isR1ToD1 = (conn.from.includes('R1') && conn.to.includes('D1')) || 
                           (conn.from.includes('D1') && conn.to.includes('R1'));
          
          if (isR1ToD1) {
            // 只有当测点在这条连线上时才返回true
            const fromCoords = extractCoordinates(conn.from);
            const toCoords = extractCoordinates(conn.to);
            
            if (!fromCoords || !toCoords) return false;
            
            return isPointOnLine(
              testPointX, testPointY,
              fromCoords.x, fromCoords.y,
              toCoords.x, toCoords.y
            );
          }
          
          return false;
        });
        
        if (hasR1ToD1Connection) {
          console.log(`  发现测点在R1和D1之间的连线上`);
          
          // 尝试添加R1
          if (!confirmedConnections.has('R1')) {
            console.log(`  基于电路连接关系添加R1`);
            
            // 查找R1组件
            const r1Component = findComponentByIdentifier('R1', componentsByType);
            
            if (r1Component) {
              testPoint.connections.push({
                type: '电阻',
                identifier: 'R1',
                standardIdentifier: 'R1',
                value: r1Component.value || '',
                unit: r1Component.unit || '',
                detail: `电阻 (R1)`,
                originalDesc: '基于电路连接关系添加',
                connectionType: 'direct',
                isOnLine: true
              });
              
              confirmedConnections.add('R1');
            }
          }
          
          // 尝试添加D1
          if (!confirmedConnections.has('D1')) {
            console.log(`  基于电路连接关系添加D1`);
            
            // 查找D1组件
            const d1Component = findComponentByIdentifier('D1', componentsByType);
            
            if (d1Component) {
              testPoint.connections.push({
                type: '二极管',
                identifier: 'D1',
                standardIdentifier: 'D1',
                value: '',
                unit: '',
                detail: `二极管 (D1)`,
                originalDesc: '基于电路连接关系添加',
                connectionType: 'direct',
                isOnLine: true
              });
              
              confirmedConnections.add('D1');
            }
          }
        }
      }
      
      // 应用电路验证规则
      applyCircuitRules(testPoint);
    });
    
    /**
     * 根据标识符查找组件
     * @param {String} identifier 组件标识符
     * @param {Map} componentsByType 按类型分组的组件
     * @returns {Object|null} 组件对象
     */
    function findComponentByIdentifier(identifier, componentsByType) {
      // 遍历所有组件类型
      for (const [type, components] of componentsByType.entries()) {
        // 查找匹配标识符的组件
        const component = components.find(comp => 
          comp.identifier === identifier || 
          comp.standardIdentifier === identifier
        );
        
        if (component) return component;
      }
      
      // 尝试从所有组件中查找
      return components.value.find(comp => 
        comp.identifier === identifier || 
        (comp.standardIdentifier && comp.standardIdentifier === identifier)
      );
    }
    
    /**
     * 应用电路验证规则
     * @param {Object} testPoint 测点对象
     */
    function applyCircuitRules(testPoint) {
      // 1. 确保没有重复的连接
      const uniqueConnections = [];
      const seenComponents = new Set();
      
      testPoint.connections.forEach(conn => {
        const compId = conn.standardIdentifier || conn.identifier || `${conn.type}_${conn.proximity}`;
        
        if (!seenComponents.has(compId)) {
          seenComponents.add(compId);
          uniqueConnections.push(conn);
        }
      });
      
      testPoint.connections = uniqueConnections;
      
      // 2. 对于特定器件组合应用规则
      // 如果同时连接了R1和D1，确保它们被保留
      const hasR1 = testPoint.connections.some(conn => conn.identifier === 'R1' || conn.standardIdentifier === 'R1');
      const hasD1 = testPoint.connections.some(conn => conn.identifier === 'D1' || conn.standardIdentifier === 'D1');
      
      if (hasR1 && hasD1) {
        console.log(`  保留关键连接: R1和D1`);
        
        // 优先保留这些连接
        testPoint.connections = testPoint.connections.filter(conn => {
          const isR1 = conn.identifier === 'R1' || conn.standardIdentifier === 'R1';
          const isD1 = conn.identifier === 'D1' || conn.standardIdentifier === 'D1';
          const isOtherCritical = conn.connectionType === 'direct' && conn.proximity && conn.proximity < 70;
          
          return isR1 || isD1 || isOtherCritical;
        });
      }
      
      // 3. 验证连接合理性
      if (testPoint.connections.some(conn => conn.type === '电容')) {
        // 检查是否连接到形成回路所需的其他组件
        const hasOtherComponents = testPoint.connections.some(conn => 
          conn.type !== '电容' && ['电阻', '二极管', '三极管', '电感'].includes(conn.type)
        );
        
        if (!hasOtherComponents) {
          console.log(`  警告: 测点只连接到电容，但缺少形成回路所需的其他组件`);
        }
      }
      
      // 4. 按连接可靠性排序
      testPoint.connections.sort((a, b) => {
        // 首先按连接类型排序
        const typeOrder = {
          'direct': 0,
          'proximity': 1,
          'inferred': 2
        };
        
        if (a.connectionType !== b.connectionType) {
          return typeOrder[a.connectionType] - typeOrder[b.connectionType];
        }
        
        // 其次按距离排序
        if (a.proximity && b.proximity) {
          return a.proximity - b.proximity;
        }
        
        return 0;
      });
      
      // 5. 限制最多显示3个最重要的连接
      if (testPoint.connections.length > 3) {
        console.log(`  连接过多，仅保留3个最重要的连接`);
        testPoint.connections = testPoint.connections.slice(0, 3);
      }
    }
    
    // 输出测点关系
    console.log('========= 测点拓扑关系(文本格式) =========');
    testPoints.forEach(testPoint => {
      // 使用序号和标识符显示测点名称
      const displayName = `测点 ${testPoint.index}(${testPoint.identifier})`;
      
      console.log(`==== ${displayName} ====`);
      
      // 对连接进行分组处理 - 按器件类型分组，但保留唯一标识符
      const connectionsByType = {};
      
      testPoint.connections.forEach(conn => {
        const type = conn.type;
        if (!connectionsByType[type]) {
          connectionsByType[type] = [];
        }
        
        // 使用标准化标识符进行比较
        const existingConn = connectionsByType[type].find(c => 
          (c.standardIdentifier && conn.standardIdentifier && c.standardIdentifier === conn.standardIdentifier) ||
          (c.identifier === conn.identifier && c.detail === conn.detail)
        );
        
        if (!existingConn) {
          connectionsByType[type].push(conn);
        }
      });
      
      // 输出连接器件 - 按类型分组但显示唯一标识符
      if (Object.keys(connectionsByType).length > 0) {
        Object.entries(connectionsByType).forEach(([type, conns]) => {
          if (conns.length === 1 && !conns[0].isTestPoint) {
            // 单个器件 - 优先使用标准化标识符
            const identifier = conns[0].standardIdentifier || conns[0].identifier;
            console.log(`连接到 ${conns[0].type}${identifier ? ' (' + identifier + ')' : ''}`);
          } else if (conns.length > 1) {
            // 多个相同类型的器件 - 优先使用标准化标识符
            const details = conns.map(c => c.standardIdentifier || c.identifier || '').filter(d => d);
            console.log(`连接到 ${type} (${details.join(', ')})`);
          } else {
            // 测点或没有详细信息的器件
            console.log(`连接到 ${conns[0].type}`);
          }
        });
      } else {
        console.log('无连接器件');
      }
      
      console.log('--------------------');
    });
    
    // 显示提示消息
    ElMessage({
      message: `已分析${testPoints.length}个测点的连接关系，请在控制台(F12)查看`,
      type: 'success',
      duration: 5000
    });
  };

  // 以下函数也是调试相关，一并注释
  /**
   * 使用器件库数据识别组件类型
   * @param {String} desc 组件描述
   * @returns {Object} 识别出的组件信息
   */
  const identifyComponentTypeFromLibrary = (desc) => {
    let bestMatch = null;
    let maxScore = 0;
    
    // 将描述转为小写以便比较
    const lowerDesc = desc.toLowerCase();
    
    // 先尝试匹配具体的电容类型
    if (lowerDesc.includes('电容') || lowerDesc.includes('capacitor')) {
      // 极性电容的特定匹配
      if (lowerDesc.includes('极性') || lowerDesc.includes('有极性') || 
          lowerDesc.includes('polarized') || lowerDesc.includes('pc')) {
        for (const component of componentsData) {
          if (component.type === 'polarizedCapacitor') {
            return {
              type: component.label,
              icon: component.icon,
              label: component.label,
              symbol: component.symbol
            };
          }
        }
      }
      
      // 无极性电容的特定匹配
      if (lowerDesc.includes('无极性') || lowerDesc.includes('non-polarized') || 
          lowerDesc.includes('非极性') || lowerDesc.includes('nc')) {
        for (const component of componentsData) {
          if (component.type === 'nonPolarizedCapacitor') {
            return {
              type: component.label,
              icon: component.icon,
              label: component.label,
              symbol: component.symbol
            };
          }
        }
      }
      
      // 可变电容的特定匹配
      if (lowerDesc.includes('可变') || lowerDesc.includes('variable') || 
          lowerDesc.includes('vc')) {
        for (const component of componentsData) {
          if (component.type === 'VariableCapacitor') {
            return {
              type: component.label,
              icon: component.icon,
              label: component.label,
              symbol: component.symbol
            };
          }
        }
      }
    }
    
    // 遍历所有组件类型进行一般匹配
    componentsData.forEach(component => {
      let score = 0;
      
      // 检查类型匹配
      if (lowerDesc.includes(component.type.toLowerCase())) {
        score += 10;
      }
      
      // 检查标签匹配
      if (lowerDesc.includes(component.label)) {
        score += 15;
      }
      
      // 检查符号匹配
      if (component.symbol && lowerDesc.includes(component.symbol.toLowerCase())) {
        score += 5;
      }
      
      // 如果是单位匹配
      if (component.unitList && component.unitList.length > 0) {
        for (const unit of component.unitList) {
          if (lowerDesc.includes(unit.toLowerCase())) {
            score += 3;
            break;
          }
        }
      }
      
      // 更新最佳匹配
      if (score > maxScore) {
        maxScore = score;
        bestMatch = component;
      }
    });
    
    // 如果没有找到匹配，使用原始的硬编码方式作为备选
    if (!bestMatch || maxScore < 5) {
      return {
        type: extractComponentTypeOriginal(desc),
        icon: null,
        label: null,
        symbol: null
      };
    }
    
    return {
      type: bestMatch.label,
      icon: bestMatch.icon,
      label: bestMatch.label,
      symbol: bestMatch.symbol
    };
  };
  
  /**
   * 原始的组件类型提取方法（作为备选）
   * @param {String} desc 组件描述
   * @returns {String} 组件类型
   */
  const extractComponentTypeOriginal = (desc) => {
    // 判断器件类型 - 精确匹配，保留原始类型
    if (desc.includes('可变电阻') || desc.includes('rheostat')) {
      return '可变电阻';
    } else if (desc.includes('电阻') || desc.includes('resistor')) {
      return '电阻';
    } else if (desc.includes('可变电容') || desc.includes('variable capacitor')) {
      return '可变电容';
    } else if (desc.includes('极性电容') || desc.includes('有极性电容') || desc.includes('polarized capacitor')) {
      return '极性电容';
    } else if (desc.includes('无极性电容') || desc.includes('non-polarized capacitor')) {
      return '无极性电容';
    } else if (desc.includes('电容') || desc.includes('capacitor')) {
      return '电容';
    } else if (desc.includes('电感') || desc.includes('inductor') || (desc.includes('L') && desc.includes('H'))) {
      return '电感';
    } else if (desc.includes('二极管') || desc.includes('diode')) {
      return '二极管';
    } else if (desc.includes('单向稳压管') || desc.includes('zener diode')) {
      return '单向稳压管';
    } else if (desc.includes('双向稳压管') || desc.includes('dual zener diode')) {
      return '双向稳压管';
    } else if (desc.includes('发光二极管') || desc.includes('LED')) {
      return '发光二极管';
    } else if (desc.includes('变压器') || desc.includes('transformer')) {
      return '变压器';
    } else if (desc.includes('PNP') || desc.includes('pnp')) {
      return 'PNP三极管';
    } else if (desc.includes('NPN') || desc.includes('npn')) {
      return 'NPN三极管';
    } else if (desc.includes('三极管') || desc.includes('transistor')) {
      return '三极管';
    } else if (desc.includes('单刀双掷开关') || desc.includes('SPDT')) {
      return '单刀双掷开关';
    } else if (desc.includes('开关') || desc.includes('switch')) {
      return '开关';
    } else if (desc.includes('按钮') || desc.includes('button') || desc.includes('pushbutton')) {
      return '按钮';
    } else if (desc.includes('继电器') || desc.includes('relay')) {
      return '继电器';
    } else if (desc.includes('电压表') || desc.includes('voltmeter')) {
      return '电压表';
    } else if (desc.includes('电流表') || desc.includes('ammeter')) {
      return '电流表';
    } else if (desc.includes('万用表') || desc.includes('multimeter')) {
      return '万用表';
    } else if (desc.includes('示波器') || desc.includes('oscilloscope')) {
      return '示波器';
    } else if (desc.includes('信号源') || desc.includes('signal')) {
      return '信号源';
    } else if (desc.includes('信号发生器') || desc.includes('signal generator')) {
      return '信号发生器';
    } else if (desc.includes('测点') || desc.includes('test point')) {
      return '测点';
    } else if (desc.includes('晶体管') || desc.includes('cristal')) {
      return '晶体管';
    } else if (desc.includes('LM78xx')) {
      return 'LM78xx';
    } else if (desc.includes('运算放大器') || desc.includes('op-amp')) {
      return '运算放大器';
    } else if (desc.includes('电压源') || desc.includes('voltage source')) {
      return '电压源';
    } else if (desc.includes('电源') || desc.includes('power') || desc.includes('battery') || desc.includes('battery')) {
      return '电源';
    } else if (desc.includes('电机') || desc.includes('motor')) {
      return '电机';
    } else if (desc.includes('保险丝') || desc.includes('fuse')) {
      return '保险丝';
    } else if (desc.includes('接地') || desc.includes('ground')) {
      return '接地';
    } else if (desc.includes('天线') || desc.includes('antenna')) {
      return '天线';
    } else if (desc.includes('扬声器') || desc.includes('speaker')) {
      return '扬声器';
    } else if (desc.includes('麦克风') || desc.includes('microphone')) {
      return '麦克风';
    } else if (desc.includes('运算放大器') || desc.includes('op-amp')) {
      return '运算放大器';
    } else if (desc.includes('逻辑门') || desc.includes('logic gate')) {
      return '逻辑门';
    } else if (desc.includes('集成电路') || desc.includes('IC') || desc.includes('integrated circuit')) {
      return '集成电路';
    }
    
    return '未知器件';
  };
  
  /**
   * 获取组件的基础类型
   * @param {String} type 组件类型
   * @returns {String} 基础类型
   */
  const getBaseComponentType = (type) => {
    if (!type) return '未知器件';
    
    // 先将类型转为小写进行匹配
    const lowerType = type.toLowerCase().replace(/[0-9\-_\.]/g, '');
    
    // 在器件库中查找匹配的类型
    for (const component of componentsData) {
      if (lowerType.includes(component.type.toLowerCase()) || 
          lowerType.includes(component.label.toLowerCase()) ||
          (component.symbol && lowerType.includes(component.symbol.toLowerCase()))) {
        return component.label;
      }
    }
    
    // 保留原始类型，仅移除数字和特殊符号
    const baseType = type.replace(/[0-9\-_\.]/g, '').toLowerCase();
    
    // 映射到标准类型，保持精确匹配
    if (baseType.includes('可变电阻') || baseType.includes('rheostat')) {
      return '可变电阻';
    } else if (baseType.includes('resistor') || baseType.includes('电阻')) {
      return '电阻';
    } else if (baseType.includes('variablecapacitor') || baseType.includes('可变电容')) {
      return '可变电容';
    } else if (baseType.includes('polarizedcapacitor') || baseType.includes('极性电容') || baseType.includes('有极性电容')) {
      return '极性电容';
    } else if (baseType.includes('nonpolarizedcapacitor') || baseType.includes('无极性电容')) {
      return '无极性电容';
    } else if (baseType.includes('capacitor') || baseType.includes('电容')) {
      return '电容';
    } else if (baseType.includes('inductor') || baseType.includes('电感') || baseType.includes('l')) {
      return '电感';
    } else if (baseType.includes('diode') || baseType.includes('二极管')) {
      return '二极管';
    } else if (baseType.includes('zenerdiode') || baseType.includes('单向稳压管')) {
      return '单向稳压管';
    } else if (baseType.includes('dualzenerdiode') || baseType.includes('双向稳压管')) {
      return '双向稳压管';
    } else if (baseType.includes('led') || baseType.includes('发光二极管')) {
      return '发光二极管';
    } else if (baseType.includes('transformer') || baseType.includes('变压器')) {
      return '变压器';
    } else if (baseType.includes('switch') || baseType.includes('开关')) {
      return '开关';
    } else if (baseType.includes('button') || baseType.includes('按钮')) {
      return '按钮';
    } else if (baseType.includes('relay') || baseType.includes('继电器')) {
      return '继电器';
    } else if (baseType.includes('pnp')) {
      return 'PNP三极管';
    } else if (baseType.includes('npn')) {
      return 'NPN三极管';
    } else if (baseType.includes('transistor') || baseType.includes('三极管')) {
      return '三极管';
    } else if (baseType.includes('voltmeter') || baseType.includes('电压表')) {
      return '电压表';
    } else if (baseType.includes('ammeter') || baseType.includes('电流表')) {
      return '电流表';
    } else if (baseType.includes('multimeter') || baseType.includes('万用表')) {
      return '万用表';
    } else if (baseType.includes('oscilloscope') || baseType.includes('示波器')) {
      return '示波器';
    } else if (baseType.includes('signalgenerator') || baseType.includes('信号发生器')) {
      return '信号发生器';
    } else if (baseType.includes('testpoint') || baseType.includes('测点')) {
      return '测点';
    } else if (baseType.includes('cristal') || baseType.includes('晶体管')) {
      return '晶体管';
    } else if (baseType.includes('power') || baseType.includes('电源') || baseType.includes('battery') || baseType.includes('电池')) {
      return '电源';
    } else if (baseType.includes('motor') || baseType.includes('电机')) {
      return '电机';
    } else if (baseType.includes('fuse') || baseType.includes('保险丝')) {
      return '保险丝';
    } else if (baseType.includes('ground') || baseType.includes('接地')) {
      return '接地';
    } else if (baseType.includes('antenna') || baseType.includes('天线')) {
      return '天线';
    } else if (baseType.includes('speaker') || baseType.includes('扬声器')) {
      return '扬声器';
    } else if (baseType.includes('microphone') || baseType.includes('麦克风')) {
      return '麦克风';
    } else if (baseType.includes('opamp') || baseType.includes('运算放大器')) {
      return '运算放大器';
    } else if (baseType.includes('logicgate') || baseType.includes('逻辑门')) {
      return '逻辑门';
    } else if (baseType.includes('ic') || baseType.includes('集成电路')) {
      return '集成电路';
    }
    
    // 如果无法匹配标准类型，则返回原始类型
    return type;
  }
  
  /**
   * 获取组件类型的标准前缀
   * @param {String} type 组件类型
   * @returns {String} 标准前缀
   */
  const getStandardPrefix = (type) => {
    // 在器件库中查找匹配的类型



    
    for (const component of componentsData) {
      if (component.label === type && component.symbol) {
        return component.symbol;
      }
    }
    
    // 如果在器件库中找不到，则使用硬编码的匹配规则
    switch (type) {
      case '电阻': return 'R';
      case '可变电阻': return 'VR';
      // 为不同类型的电容提供更具区分性的前缀
      case '电容': return 'C';
      case '无极性电容': return 'C'; // 仍使用C但在器件数据中标记
      case '极性电容': return 'PC'; // 使用PC表示极性电容
      case '可变电容': return 'VC'; // 使用VC表示可变电容
      case '电感': return 'L';
      case '二极管': return 'D';
      case '单向稳压管': return 'ZD';
      case '双向稳压管': return 'DZD';
      case '发光二极管': return 'LED'; // 使用更明确的LED
      case '变压器': return 'T';
      case '三极管': return 'Q';
      case 'PNP三极管': return 'Q';
      case 'NPN三极管': return 'Q';
      case '单刀单掷开关': return 'S';
      case '单刀双掷开关': return 'SPDT';
      case '开关': return 'S';
      case '按钮': return 'S';
      case '继电器': return 'K';
      case '电压表': return 'V';
      case '电流表': return 'A';
      case '万用表': return 'M';
      case '示波器': return 'OSC';
      case '信号发生器': return 'G';
      case '测点': return 'TP';
      case '晶体管': return 'Y';
      case '电源': return 'PS';
      case '直流电压源': return 'V';
      case '电机': return 'M';
      case '保险管': return 'F';
      case '接地': return 'GND';
      case '天线': return 'ANT';
      case '扬声器': return 'SP';
      case '麦克风': return 'MIC';
      case '三端稳压集成电路': return 'U';
      case '运算放大器': return 'U';
      case '逻辑门': return 'U';
      case '集成电路': return 'U';
      default: return type.charAt(0).toUpperCase();
    }
  }

  /**
   * 判断点是否在线段上
   * @param {Number} px 点的x坐标
   * @param {Number} py 点的y坐标
   * @param {Number} x1 线段起点x坐标
   * @param {Number} y1 线段起点y坐标
   * @param {Number} x2 线段终点x坐标
   * @param {Number} y2 线段终点y坐标
   * @returns {Boolean} 点是否在线段上
   */
  function isPointOnLine(px, py, x1, y1, x2, y2) {
    // 计算线段长度
    const lineLength = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    
    // 计算点到线段两端的距离
    const d1 = Math.sqrt(Math.pow(px - x1, 2) + Math.pow(py - y1, 2));
    const d2 = Math.sqrt(Math.pow(px - x2, 2) + Math.pow(py - y2, 2));
    
    // 计算点到线段的距离
    const lineToPointDistance = distancePointToLine(px, py, x1, y1, x2, y2);
    
    // 一般在电路图上的连线是水平、垂直或45度角的
    // 对于水平线，y坐标应该相近
    const isHorizontalLine = Math.abs(y1 - y2) < 5;
    // 对于垂直线，x坐标应该相近
    const isVerticalLine = Math.abs(x1 - x2) < 5;
    
    // 判断点到线段的距离是否小于阈值
    const ON_LINE_THRESHOLD = 5; // 5像素阈值
    const isCloseToLine = lineToPointDistance < ON_LINE_THRESHOLD;
    
    // 判断点是否在线段范围内
    const isWithinLineRange = Math.abs(d1 + d2 - lineLength) < 1; // 允许1像素误差
    
    // 如果是水平线，检查y坐标是否接近
    if (isHorizontalLine && Math.abs(py - y1) < ON_LINE_THRESHOLD) {
      // 检查x坐标是否在范围内
      return px >= Math.min(x1, x2) - ON_LINE_THRESHOLD && 
             px <= Math.max(x1, x2) + ON_LINE_THRESHOLD;
    }
    
    // 如果是垂直线，检查x坐标是否接近
    if (isVerticalLine && Math.abs(px - x1) < ON_LINE_THRESHOLD) {
      // 检查y坐标是否在范围内
      return py >= Math.min(y1, y2) - ON_LINE_THRESHOLD && 
             py <= Math.max(y1, y2) + ON_LINE_THRESHOLD;
    }
    
    // 对于其他角度的线段，使用点到线段的距离和范围判断
    return isCloseToLine && isWithinLineRange;
  }
  
  /**
   * 计算点到线段的距离
   * @param {Number} px 点的x坐标
   * @param {Number} py 点的y坐标
   * @param {Number} x1 线段起点x坐标
   * @param {Number} y1 线段起点y坐标
   * @param {Number} x2 线段终点x坐标
   * @param {Number} y2 线段终点y坐标
   * @returns {Number} 点到线段的距离
   */
  function distancePointToLine(px, py, x1, y1, x2, y2) {
    // 线段长度的平方
    const l2 = Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2);
    
    // 如果线段长度为0，返回点到端点的距离
    if (l2 === 0) return Math.sqrt(Math.pow(px - x1, 2) + Math.pow(py - y1, 2));
    
    // 计算点在线段上的投影位置 t
    const t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / l2;
    
    // 如果 t < 0，点的投影在线段外部，最近点是线段的起点
    if (t < 0) return Math.sqrt(Math.pow(px - x1, 2) + Math.pow(py - y1, 2));
    
    // 如果 t > 1，点的投影在线段外部，最近点是线段的终点
    if (t > 1) return Math.sqrt(Math.pow(px - x2, 2) + Math.pow(py - y2, 2));
    
    // 计算投影点坐标
    const projX = x1 + t * (x2 - x1);
    const projY = y1 + t * (y2 - y1);
    
    // 返回点到投影点的距离
    return Math.sqrt(Math.pow(px - projX, 2) + Math.pow(py - projY, 2));
  }

  /**
   * 保存当前电路为模板
   */
  const saveTemplate = async () => {
    try {
      // 🔧 移除测点计数器重置，现在使用组件ID作为标识符
      // window.testPointCounter = 0;

      // 弹出输入框让用户输入模板名称
      const templateName = prompt('请输入模板名称:', `模板_${new Date().toLocaleDateString()}`)

      if (!templateName) {
        ElMessage.info('已取消保存模板')
        return
      }

      // 保存模板
      const template = saveAsTemplate(templateName)

      if (template) {
      }
    } catch (error) {
      console.error('保存模板时出错:', error)
      ElMessage.error('保存模板失败')
    }
  }

  /**
   * 匹配当前电路与模板
   */
  const matchTemplate = async () => {
    try {
      // 🔧 移除测点计数器重置，现在使用组件ID作为标识符
      // window.testPointCounter = 0;

      // 获取已保存的模板列表
      const templates = getStoredTemplates()
      const templateNames = Object.keys(templates)

      if (templateNames.length === 0) {
        ElMessage.warning('没有找到已保存的模板，请先保存一个模板')
        return
      }

      // 如果只有一个模板，直接使用
      let selectedTemplate = templateNames[0]

      // 如果有多个模板，让用户选择
      if (templateNames.length > 1) {
        const templateList = templateNames.map((name, index) => `${index + 1}. ${name}`).join('\n')
        const selection = prompt(`请选择要匹配的模板（输入序号）:\n${templateList}`)

        if (!selection) {
          ElMessage.info('已取消匹配')
          return
        }

        const index = parseInt(selection) - 1
        if (index >= 0 && index < templateNames.length) {
          selectedTemplate = templateNames[index]
        } else {
          ElMessage.error('无效的选择')
          return
        }
      }

      // 执行匹配
      const matchResult = await matchWithTemplate(selectedTemplate)

      if (matchResult) {
        // 显示详细的匹配结果
        displayMatchResult(matchResult)
      }
    } catch (error) {
      console.error('匹配模板时出错:', error)
      ElMessage.error('匹配模板失败')
    }
  }


</script>

<style scoped lang="less">
  /* 定义基础变量 */
  @toolbar-bg-color: #f3f6f8; /* 工具栏背景色 */
  @button-gap: 12px; /* 统一按钮间距 */
  @button-radius: 8px; /* 更圆润的按钮圆角 */
  @button-padding: 6px 10px; /* 按钮内边距 */
  @button-font-size: 13px; /* 按钮字体大小 */
  @danger-bg-color: #fef0f0; /* 危险按钮背景色 */
  @danger-hover-bg-color: #fde2e2; /* 危险按钮悬浮背景色 */
  @danger-text-color: #f56c6c; /* 危险按钮文字颜色 */

  /* 工具栏整体样式 */
  .tool-bar {
    display: flex; /* 横向排列按钮 */
    flex-wrap: wrap; /* 超出时自动换行 */
    gap: 12px; /* 减小按钮间距 */
    padding: 8px 16px; /* 减小工具栏内边距 */
    background-color: #ffffff; /* 纯白背景 */
    border-bottom: 1px solid #e5e7eb; /* 更淡的分隔线 */
    align-items: center; /* 垂直居中对齐 */
  }

  /* 按钮样式 */
  .tool-bar .el-button {
    display: flex; /* 图标和文字水平对齐 */
    align-items: center; /* 垂直居中 */
    gap: 5px; /* 图标与文字之间的间距 */
    font-size: @button-font-size; /* 按钮文字大小 */
    padding: @button-padding; /* 按钮内边距 */
    border-radius: @button-radius; /* 按钮圆角 */
    transition:
      background-color 0.3s,
      box-shadow 0.3s; /* 添加悬浮动画效果 */

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
    }
  }

  /* 危险按钮样式 */
  .tool-bar .el-button[type='danger'] {
    background-color: @danger-bg-color; /* 危险按钮背景色 */
    color: @danger-text-color; /* 危险按钮文字颜色 */

    &:hover {
      background-color: @danger-hover-bg-color; /* 悬浮时的危险背景色 */
    }
  }

  /* 图标样式 */
  .tool-bar .el-icon {
    font-size: 16px; /* 图标大小 */
    color: inherit; /* 图标颜色跟随文字颜色 */
  }

  /* 为测试按钮添加特殊样式，使其更醒目 */
  .el-button[style*="background-color: #8e44ad"] {
    &:hover {
      background-color: #9b59b6 !important;
      border-color: #9b59b6 !important;
    }
  }

  /* 工具栏整体样式 - 简约扁平化 */
  .tool-bar {
    background: #ffffff;
    padding: 8px 16px; /* 减少内边距 */
    border: none;
    border-bottom: 1px solid #e5e7eb; /* 只保留底部分隔线 */
    box-shadow: none; /* 去掉阴影 */
    margin-bottom: 0; /* 去掉下边距 */
  }

  /* 工具栏流式布局 - 简约化 */
  .toolbar-flow {
    display: flex;
    align-items: center;
    gap: 12px; /* 适中的间距 */
    flex-wrap: wrap;
    padding: 4px 0; /* 减少垂直内边距 */
  }

  /* 调试分隔符 */
  .debug-separator {
    width: 1px;
    height: 24px;
    background: #e2e8f0;
    margin: 0 12px;
  }

  /* 连线模式样式 - 简约化 */
  .mode-wrapper {
    display: flex;
    align-items: center;
    gap: 6px; /* 减小间距 */
    padding: 6px 12px; /* 减小内边距 */
    background: #f9fafb; /* 简单的背景色 */
    border: 1px solid #e5e7eb; /* 淡边框 */
    border-radius: 8px; /* 更圆润的圆角 */
    box-shadow: none; /* 去掉阴影 */
    transition: all 0.15s ease;

    &:hover {
      background: #f3f4f6;
      border-color: #d1d5db;
    }
  }

  .mode-icon {
    color: #64748b;
    transition: color 0.2s ease;
  }

  .mode-wrapper:hover .mode-icon {
    color: #475569;
  }

  .mode-label {
    font-size: 13px;
    font-weight: 400; /* 减轻字重 */
    color: #374151;
    white-space: nowrap;
    user-select: none;
  }



  /* 通用按钮样式 - 简约扁平化 */
  .action-btn {
    border-radius: 8px !important; /* 更圆润的圆角 */
    transition: all 0.15s ease !important; /* 更快的过渡 */
    border: 1px solid #e5e7eb !important; /* 更淡的边框 */
    font-weight: 400 !important; /* 减轻字重 */
    font-size: 13px !important;
    padding: 6px 10px !important; /* 减小内边距 */
    background: #ffffff !important;
    color: #374151 !important;
    box-shadow: none !important; /* 去掉阴影 */

    &:hover {
      background: #f3f4f6 !important; /* 更淡的悬停色 */
      border-color: #d1d5db !important;
      transform: none !important; /* 去掉位移动画 */
      box-shadow: none !important; /* 去掉悬停阴影 */
    }

    &:active {
      background: #e5e7eb !important;
      transform: none !important;
    }

    &:disabled {
      background: #f9fafb !important;
      color: #9ca3af !important;
      border-color: #e5e7eb !important;
      box-shadow: none !important;
      transform: none !important;
    }

    span {
      margin-left: 3px; /* 减小图标文字间距 */
      font-size: 13px;
    }
  }

  /* 编辑按钮 */
  .edit-btn {
    background: #3b82f6 !important;
    color: white !important;
    border-color: #2563eb !important;

    &:hover {
      background: #2563eb !important;
      border-color: #1d4ed8 !important;
    }
  }

  /* 删除按钮 */
  .delete-btn {
    background: #ef4444 !important;
    color: white !important;
    border-color: #dc2626 !important;

    &:hover {
      background: #dc2626 !important;
      border-color: #b91c1c !important;
    }
  }

  /* 添加按钮 */
  .add-btn {
    background: #10b981 !important;
    color: white !important;
    border-color: #059669 !important;

    &:hover {
      background: #059669 !important;
      border-color: #047857 !important;
    }
  }



  /* 校验按钮 */
  .verify-btn {
    background: #f59e0b !important;
    color: white !important;
    border-color: #d97706 !important;

    &:hover {
      background: #d97706 !important;
      border-color: #b45309 !important;
    }
  }

  /* 保存按钮 */
  .save-btn {
    background: #8b5cf6 !important;
    color: white !important;
    border-color: #7c3aed !important;

    &:hover {
      background: #7c3aed !important;
      border-color: #6d28d9 !important;
    }
  }

  /* 重置按钮 */
  .reset-btn {
    background: #f97316 !important;
    color: white !important;
    border-color: #ea580c !important;

    &:hover {
      background: #ea580c !important;
      border-color: #c2410c !important;
    }
  }

  /* 完成按钮 */
  .finish-btn {
    background: #10b981 !important;
    color: white !important;
    border-color: #059669 !important;

    &:hover {
      background: #059669 !important;
      border-color: #047857 !important;
    }
  }



  /* 调试按钮样式 */
  .debug-btn {
    border-radius: 8px !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
    border: 1px solid !important;
    transition: all 0.2s ease !important;

    span {
      margin-left: 3px;
      font-size: 11px;
    }

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }
  }

  .info-debug {
    background: #0ea5e9 !important;
    color: white !important;
    border-color: #0284c7 !important;

    &:hover {
      background: #0284c7 !important;
      border-color: #0369a1 !important;
    }
  }

  .warning-debug {
    background: #f59e0b !important;
    color: white !important;
    border-color: #d97706 !important;

    &:hover {
      background: #d97706 !important;
      border-color: #b45309 !important;
    }
  }

  .danger-debug {
    background: #ef4444 !important;
    color: white !important;
    border-color: #dc2626 !important;

    &:hover {
      background: #dc2626 !important;
      border-color: #b91c1c !important;
    }
  }

  .success-debug {
    background: #10b981 !important;
    color: white !important;
    border-color: #059669 !important;

    &:hover {
      background: #059669 !important;
      border-color: #047857 !important;
    }
  }

  /* 跳转实验环境按钮 */
  .experiment-btn {
    background: #8b5cf6 !important;
    color: white !important;
    border-color: #7c3aed !important;

    &:hover {
      background: #7c3aed !important;
      border-color: #6d28d9 !important;
    }

    span {
      margin-left: 4px;
      font-weight: 500;
      font-size: 12px;
    }
  }

  /* 旋转按钮美化样式 */
  .rotate-btn {
    background: #3b82f6 !important;
    color: white !important;
    border: 1px solid #2563eb !important;

    &:hover {
      background: #2563eb !important;
      border-color: #1d4ed8 !important;
    }

    &:disabled {
      background: #f1f5f9 !important;
      color: #94a3b8 !important;
      border-color: #e2e8f0 !important;
    }

    /* 图标和文字间距优化 */
    span {
      margin-left: 4px;
      font-weight: 500;
      font-size: 12px;
    }
  }

  /* 小尺寸旋转按钮样式 */
  .rotate-btn-small {
    padding: 6px 10px !important;
    font-size: 11px !important;
    min-width: auto !important;

    span {
      margin-left: 3px;
      font-size: 11px;
      font-weight: 500;
    }
  }
</style>