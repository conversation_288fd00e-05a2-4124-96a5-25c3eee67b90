import { ElMessageBox } from 'element-plus'
import { get } from '@/api/circuitData'
import { loadData, saveData } from '@/utils/canvasDataManager'
import { checkUserExist } from '@/utils/before/checkUserExists'

/**
 * 校验参数合法性
 */
export function validateURLParam() {
  // 首先从URL获取参数
  const urlParams = getURLParam()

  // 如果URL中有参数，保存到localStorage
  if (urlParams.userId || urlParams.courseName || urlParams.experimentName) {
    if (urlParams.userId) {
      localStorage.setItem('userId', urlParams.userId)
    }
    if (urlParams.courseName) {
      localStorage.setItem('courseName', urlParams.courseName)
    }
    if (urlParams.experimentName) {
      localStorage.setItem('experimentName', urlParams.experimentName)
    }
  }

  // 然后从localStorage获取参数（确保一致性）
  const { userId, courseName, experimentName } = getLocalStorgeParam()

  // // 校验参数合法性：不合法就给提示然后关闭页面
  // const isValid = (value) => value && value.trim() !== ''

  // if (![userId, courseName, experimentName].every(isValid)) {
  //   ElMessageBox.alert('URL 参数不正确！ <br> 点击<strong>【收到】</strong>后，将<strong>【自动关闭】</strong>此页面！', '警告', {
  //     confirmButtonText: '收到',
  //     type: 'warning',
  //     dangerouslyUseHTMLString: true,
  //     callback: () => {
  //       window.close()
  //     },
  //   })
  // }

  // 校验是否是系统用户
  // checkUserExist()

  // 参数合法，返回
  return { userId, courseName, experimentName }
}

/**
 * 从数据库中查询电路图参数, 并加载到 store
 */
export function getCircuitDataAndLoadData() {
  // 请求体
  const urlParams = validateURLParam()

  // 请求数据
  get(urlParams).then((res) => {
    const { code, msg, data } = res.data
    if (code == 200) {
      if (data && data.circuitData) {
        loadData(data.circuitData) // 从数据库查询
      } else {
        // 创建一个空数据
        saveData()
      }
    }
  })
}

export function getURLParam() {
  // 获取 URL 查询参数
  const urlParams = new URLSearchParams(window.location.search)
  const userId = urlParams.get('userId') || ''
  const courseName = urlParams.get('courseName') || ''
  const experimentName = urlParams.get('experimentName') || ''

  return { userId, courseName, experimentName }
}

export function getLocalStorgeParam() {
  // 从本地存储中获取参数
  const userId = localStorage.getItem('userId')
  const courseName = localStorage.getItem('courseName')
  const experimentName = localStorage.getItem('experimentName')

  return { userId, courseName, experimentName }
}
