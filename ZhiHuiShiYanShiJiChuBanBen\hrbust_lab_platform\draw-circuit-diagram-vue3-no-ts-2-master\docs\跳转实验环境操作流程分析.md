# 跳转实验环境操作流程分析

## 概述
本文档详细分析了从电路图编辑器跳转到实验环境的完整操作流程，包括数据收集、验证、传输和commObj对象构建过程。

## 1. 跳转触发方式

### 主要入口
- **ButtonToolBar/index.vue**：主要的"跳转到实验环境"按钮
- **GoToExperiment/index.vue**：备用的"实验环境"按钮

### 触发条件
- 电路图校验通过
- 数据保存成功
- 组件数据收集完成

## 2. 跳转前的准备工作

### 2.1 校验阶段
```javascript
const verifyResult = await verify();
if (!verifyResult) {
    ElMessage.warning('校验未通过，请修正电路后再试');
    return;
}
```

**校验内容：**
- 电路连接关系验证
- 测试点分组验证
- 组件类型匹配验证

### 2.2 数据保存
```javascript
await saveData(); // 保存当前电路图数据到数据库
```

### 2.3 数据清理
```javascript
localStorage.removeItem('ammeters');     // 清除旧的电流表数据
localStorage.removeItem('voltmeters');   // 清除旧的电压表数据
```

**目的：** 避免旧数据干扰新的实验环境

## 3. 组件数据收集

### 3.1 可变电阻组件
```javascript
// 收集所有rheostat组件信息
const variableResistors = [];
// 包含：id, identifier, currentValue, minValue, maxValue等
```

### 3.2 电流表组件
```javascript
// 收集所有ammeter组件信息
const ammeters = [];
// 包含：id, identifier, type, range等
```

### 3.3 电压表组件
```javascript
// 收集所有voltmeter组件信息
const voltmeters = [];
// 包含：id, identifier, type, range等
```

### 3.4 测试点数据处理
```javascript
// 计算测试点位置（网格坐标）
const gridX = Math.round(centerX / gridSize);
const gridY = Math.round(centerY / gridSize);
const percentX = (gridX * gridSize / canvasWidth) * 100;
const percentY = (gridY * gridSize / canvasHeight) * 100;
```

**关键特性：**
- 使用网格坐标系统
- 计算百分比位置
- 保存原始计算值用于调试

## 4. 数据存储到localStorage

### 4.1 存储的数据类型
```javascript
localStorage.setItem('circuit_test_points', testPointsJSON);
localStorage.setItem('ammeters', ammetersJSON);
localStorage.setItem('voltmeters', voltmetersJSON);
localStorage.setItem('variableResistors', variableResistorsJSON);
localStorage.setItem('commObjData', commObjDataJSON);  // 🎯 关键数据
```

### 4.2 会话存储
```javascript
sessionStorage.setItem('from_circuit_editor', 'true');
sessionStorage.setItem('circuit_grid_size', gridSize.toString());
```

## 5. commObj对象构建流程

### 5.1 数据来源链路

#### 数据库接口
```javascript
// src/api/circuitDataTemplate.js
export function get(data) {
  return service({
    url: '/circuitDataTemplate/get',  // 🎯 主要数据源
    method: 'post',
    data,
  })
}
```

#### 数据读取流程
```javascript
const verify = async () => {
  const courseName = localStorage.getItem('courseName')
  const experimentName = localStorage.getItem('experimentName')
  
  await get({ courseName, experimentName })
    .then((res) => {
      // 🔍 从TEST_POINT_RELATIONS列读取数据
      if (response.data.testPointRelations) {
        testPointRequirements = JSON.parse(response.data.testPointRelations)
      }
    })
}
```

### 5.2 commObjData构建
```javascript
const commObjData = {}
Object.keys(testPointRequirements).forEach(groupKey => {
  const group = testPointRequirements[groupKey]
  if (group.testPoint) {
    const testPoint = group.testPoint
    const identifier = testPoint.identifier
    commObjData[identifier] = {
      bval: testPoint.bval || testPoint.identifier,
      channel: testPoint.channel || "1"  // 🎯 从数据库TEST_POINT_RELATIONS列读取
    }
  }
})
```

### 5.3 数据库字段结构
```javascript
// testPoint对象结构（来自TEST_POINT_RELATIONS列）
{
  identifier: "测点标识符",
  bval: "硬件标识值", 
  channel: "通道值"  // 🎯 关键字段，如"1", "2", "1,2"等
}
```

## 6. 页面跳转

### 6.1 目标页面
```javascript
const experimentWindow = window.open(
  '/hrbust_lab_platform/draw-circuit-diagram-dist/syhj.html', 
  '_blank'
);
```

### 6.2 实验环境页面加载流程
1. **页面初始化**：`setTimeout(loadTestPoints, 1000)`
2. **数据读取**：从localStorage读取传递的数据
3. **commObj构建**：调用`buildCommObjFromTestPointRelations()`
4. **仪表初始化**：根据组件数据创建仪表界面
5. **测点连接**：建立测点与硬件通道的映射关系

## 7. 关键问题与解决方案

### 7.1 数据来源优先级
```javascript
function buildCommObjFromTestPointRelations() {
    // 🎯 第一优先级：使用localStorage中的正确commObjData
    let correctCommObjData = localStorage.getItem('commObjData');
    if (correctCommObjData) {
        const correctData = JSON.parse(correctCommObjData);
        Object.assign(commObj, correctData);
        return true;
    }
    // 第二优先级：从testPointRelations重新构建
}
```

### 7.2 数据覆盖问题修复
**问题：** bsdlist数据会覆盖正确的commObjData
**解决：** 优先使用localStorage中的正确数据，避免被后续数据覆盖

### 7.3 fallback逻辑修复
```javascript
// ❌ 修复前：使用分组ID作为channel
channel: testPoint.channel || groupKey  // groupKey是4、6等分组ID

// ✅ 修复后：使用默认通道值
channel: testPoint.channel || "1"       // 使用有效通道值
```

## 8. 最终commObj结构示例
```json
{
  "1": {"bval":"31", "channel":"2"},
  "2": {"bval":"0", "channel":"1,2"},    // 多通道支持
  "3": {"bval":"2", "channel":"1"},
  "4": {"bval":"1", "channel":"1"},
  "5": {"bval":"3", "channel":"1"},
  "6": {"bval":"33", "channel":"2"}
}
```

## 9. 调试建议

### 9.1 数据库数据验证
```javascript
console.log('🔍 === 数据库TEST_POINT_RELATIONS列原始数据 ===')
console.log('原始字符串:', response.data.testPointRelations)
console.log('数据类型:', typeof response.data.testPointRelations)
```

### 9.2 解析结果验证
```javascript
console.log('🔍 处理测点数据:', {
  groupKey,
  identifier,
  testPoint,
  'testPoint.channel': testPoint.channel,
  'testPoint.bval': testPoint.bval
})
```

## 10. 数据构建过程中的覆盖问题分析

### 10.1 问题根源
**发现的核心问题**：localStorage中存储的commObjData与实际需要的值完全不匹配

**当前localStorage数据：**
```json
{
  "1": {"bval": "1", "channel": "1"},
  "2": {"bval": "2", "channel": "1"},
  "3": {"bval": "3", "channel": "1"},
  "4": {"bval": "4", "channel": "1"},
  "5": {"bval": "5", "channel": "1"},
  "6": {"bval": "6", "channel": "1"},
  "7": {"bval": "7", "channel": "1"}
}
```

**实际需要的数据：**
```json
{
  "1": {"bval": "31", "channel": "2"},
  "2": {"bval": "0", "channel": "1,2"},
  "3": {"bval": "2", "channel": "1"},
  "4": {"bval": "1", "channel": "1"},
  "5": {"bval": "3", "channel": "1"},
  "6": {"bval": "33", "channel": "2"}
}
```

### 10.2 数据覆盖链路分析

#### 第一层：数据库数据读取
```javascript
// ButtonToolBar/index.vue - verify()函数
await get({ courseName, experimentName })
  .then((res) => {
    if (response.data.testPointRelations) {
      testPointRequirements = JSON.parse(response.data.testPointRelations)
      // 🎯 这里的testPoint.bval和testPoint.channel应该是正确的
    }
  })
```

#### 第二层：本地测点identifier生成
```javascript
// ButtonToolBar/index.vue - debugTestPointRelations()函数
testPoints.forEach((testPoint) => {
  testPoints.push({
    id: component.id || component.componentId,
    identifier: component.identifier || '',  // 🚨 可能为空或简单序号
    x: component.x,
    y: component.y,
    component: component
  })
})

// 使用简单计数器生成identifier
let testPointCounter = 0
testPoints.forEach((testPoint) => {
  if (surroundingComponents.length > 0) {
    testPointCounter++
    const groupId = testPointCounter.toString()  // 🚨 生成1,2,3,4,5,6,7
  }
})
```

#### 第三层：数据构建时的覆盖
```javascript
// commObjData构建时
const identifier = testPoint.identifier  // 🚨 使用了本地生成的简单序号
commObjData[identifier] = {
  bval: testPoint.bval || testPoint.identifier,  // 🚨 fallback到简单序号
  channel: testPoint.channel || "1"
}
```

### 10.3 问题分析

1. **identifier来源错误**：使用本地生成的简单序号(1,2,3...)而非数据库的真实标识符
2. **bval值错误**：当testPoint.bval为空时，fallback到简单序号identifier
3. **channel值丢失**：数据库的真实channel值(如"1,2", "2")被默认值"1"覆盖

### 10.4 解决方案

#### 🎯 方案1：实现页面初始化用户角色判断逻辑（已实现）
**问题根源**：页面初始化时所有用户都在使用`/circuitData/get`获取个人数据，而老师应该获取模板数据

**解决方案**：修改`src/utils/before/validateURLParam.js`中的`getCircuitDataAndLoadData()`函数
```javascript
// 1. 添加角色检查函数
async function checkUserRole() {
  const userId = localStorage.getItem('userId')
  const response = await getRolesByUserId(userId)
  const roleList = response.data.data
  const teacherRoles = ['系统超管', '教师', '管理员', 'admin', 'teacher', 'Admin', 'ADMIN']
  return teacherRoles.some(role => roleList.includes(role))
}

// 2. 修改页面初始化数据加载逻辑
export async function getCircuitDataAndLoadData() {
  const isTeacher = await checkUserRole()
  const apiFunction = isTeacher ? getTemplate : getCircuitData
  const apiName = isTeacher ? '/circuitDataTemplate/get' : '/circuitData/get'

  const res = await apiFunction(urlParams)
  // 根据角色处理不同的数据结构
  if (isTeacher) {
    // 老师加载模板数据
    loadData(data.templateData || data.connections)
  } else {
    // 学生加载个人数据
    loadData(data.circuitData)
  }
}
```

**页面初始化流程**：
1. **App.vue** → `onMounted()` → `getCircuitDataAndLoadData()`
2. **角色检查** → 确定用户是老师还是学生
3. **API选择** → 老师用模板API，学生用个人数据API
4. **数据加载** → 根据角色加载对应的数据结构

**API接口区分**：
- **老师/管理员** → `/circuitDataTemplate/get` → 获取模板数据（标准答案）
- **学生** → `/circuitData/get` → 获取自己保存的电路数据

#### 方案2：修复identifier映射关系
建立本地测点组件与数据库测点数据的正确映射关系

#### 方案3：数据验证和调试
```javascript
console.log('🔍 === 数据库测点数据详细分析 ===')
console.log('分组Key:', groupKey)
console.log('测点标识符:', identifier)
console.log('bval值:', testPoint.bval, '(类型:', typeof testPoint.bval, ')')
console.log('channel值:', testPoint.channel, '(类型:', typeof testPoint.channel, ')')
```

## 11. 总结

跳转实验环境的核心是确保数据的正确传递和commObj对象的准确构建。关键点包括：

### 🎯 **核心修复**：页面初始化用户角色判断逻辑（已实现）
1. **页面初始化流程修复**：
   - 修改`src/utils/before/validateURLParam.js`中的`getCircuitDataAndLoadData()`函数
   - 在`App.vue`的`onMounted()`生命周期中正确加载数据
2. **API接口区分**：
   - 老师/管理员 → `/circuitDataTemplate/get` → 获取模板数据（标准答案）
   - 学生 → `/circuitData/get` → 获取自己保存的电路数据
3. **角色检查机制**：通过`getRolesByUserId()`API检查用户角色
4. **数据结构处理**：根据角色处理不同的数据结构（templateData vs circuitData）

### 📋 **其他关键要素**
1. **数据来源明确**：channel值必须从数据库TEST_POINT_RELATIONS列读取
2. **identifier映射正确**：确保本地测点组件与数据库数据的正确对应关系
3. **避免数据覆盖**：防止本地生成的简单序号覆盖数据库的真实值
4. **fallback逻辑修复**：避免使用分组ID或简单序号作为通道值
5. **数据完整性验证**：确保所有组件数据正确收集和传递
6. **调试支持增强**：添加详细的日志追踪数据流和覆盖过程

### 🔧 **预期效果**
通过实现用户角色判断逻辑，学生现在应该能够：
- 获取自己保存的电路数据而不是模板数据
- 获得正确的bval和channel值（如bval="31", channel="2"）
- 在实验环境中实现硬件设备的准确控制

这个修复解决了数据来源错误的根本问题，确保电路图编辑器中的设计能够正确地在实验环境中运行。
